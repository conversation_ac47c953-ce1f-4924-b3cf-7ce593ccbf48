
3. 2025题目三：唯一索引

前置题目：查询执行

题目种类：基础功能

推荐知识点：B+树索引

代码框架：https://gitlab.eduxiji.net/csc1/csc-db/db2025/-/tree/main/rmdb


题目描述：

在现有系统的基础上增添索引功能，该索引为唯一索引，要求系统能够支持创建索引、删除索引、展示某个表上的索引、单点查询和范围查询，实现索引与基表的同步。

如果一个表上的某个字段建有唯一索引，则该表中任意两个记录中该字段的值不相同。

 

提示：

（1）功能题目对代码框架没有限制，参赛队伍可以修改、增添、删除数据结构及接口，也可以对框架进行重构。

（2）磁盘数据库中常用的索引为B+树索引，推荐使用B+树来实现索引功能，参赛队伍也可以使用其他类型的索引，但需要能够支持题目中要求的功能。

（3）对于测试点2、3，即“索引的查询”和“维护索引的插入、删除、更新”，会进行单独的是否真正使用了索引的测试——对于数千条查询语句，建立索引后的执行时间应该小于建立索引前的执行时间的70%，才可以视为真正使用了索引的测试。

（4）改题目不要求使用B+树作为索引，所以对于参赛队伍测试点2、3的输出结果不要求行的顺序与期待输出的行的顺序完全一致，但列的顺序要求完全一致。

 

1、创建、删除、展示索引

（1）支持单个字段索引的创建和删除；

（2）支持多个字段索引的创建和删除；

（3）查看某个表上的索引信息；

（4）show index from table_name的输出格式为 | table_name | unique | (column_name, column_name) |。参考下方测试用例和期待输出

 

测试示例：

create table warehouse (id int, name char(8));

create index warehouse (id);

show index from warehouse;

create index warehouse (id,name);

show index from warehouse;

drop index warehouse (id);

drop index warehouse (id,name);

show index from warehouse;

期望输出： 

| warehouse | unique | (id) | // 第一个show index的输出

| warehouse | unique | (id) | // 第二个show index的输出

| warehouse | unique | (id,name) | // 第二个show index的输出

// 第三个show index没有输出

 

2、索引查询

在创建索引后，能够使用索引进行单点查询和范围查询。

 

提示：

在大赛提供的框架中，只有查询条件与索引完全一致，并且是单点查询时，才使用索引来进行查询，你需要对索引匹配规则进行修改，要求使用最左匹配原则。例如，表A的(id, name, score)三个属性上有一个联合索引，对于以下几种查询，都需要使用索引来进行查询：

l select * from A where id = 1 and name = 'abcd' and score = 99.0;

l select * from A where id = 1 and name = 'abcd' and score > 90.0;

l select * from A where id = 1 and name = 'abcd';

l select * from A where name = 'abcd' and id = 1;  // 在进行查询计划生成时，应能够自动对顺序进行调整

l select * from A where id = 1;

l select * from A where id > 1;

 

测试示例：

create table warehouse (w_id int, name char(8));

insert into warehouse values (10 , 'qweruiop');

insert into warehouse values (534, 'asdfhjkl');

insert into warehouse values (100,'qwerghjk');

insert into warehouse values (500,'bgtyhnmj');

create index warehouse(w_id);

select * from warehouse where w_id = 10;

select * from warehouse where w_id < 534 and w_id > 100;

drop index warehouse(w_id);

create index warehouse(name);

select * from warehouse where name = 'qweruiop';

select * from warehouse where name > 'qwerghjk';

select * from warehouse where name > 'aszdefgh' and name < 'qweraaaa';

drop index warehouse(name);

create index warehouse(w_id,name);

select * from warehouse where w_id = 100 and name = 'qwerghjk';

select * from warehouse where w_id < 600 and name > 'bztyhnmj';

期待输出：

| w_id | name |

| 10 | qweruiop |

| w_id | name |

| 500 | bgtyhnmj |

| w_id | name |

| 10 | qweruiop |

| w_id | name |

| 10 | qweruiop |

| w_id | name |

| 500 | bgtyhnmj |

| w_id | name |

| 100 | qwerghjk |

| w_id | name |

| 10 | qweruiop |

| 100 | qwerghjk |

 

 

3、索引维护

在建有索引的表中插入、删除、更新数据时，能够根据表数据的变化同步对表上对索引进行更新，保证索引的正确性。同时，在索引被更新时，需要检查唯一性约束。

 

测试示例：

create table warehouse (w_id int, name char(8));

insert into warehouse values (10 , 'qweruiop');

insert into warehouse values (534, 'asdfhjkl');

select * from warehouse where w_id = 10;

select * from warehouse where w_id < 534 and w_id > 100;

create index warehouse(w_id);

insert into warehouse values (500, 'lastdanc');

update warehouse set w_id = 507 where w_id = 534;

select * from warehouse where w_id = 10;

select * from warehouse where w_id < 534 and w_id > 100;

期待输出：

| w_id | name |

| 10 | qweruiop |

| w_id | name |

| w_id | name |

| 10 | qweruiop |

| w_id | name |

| 500 | lastdanc |

| 507 | asdfhjkl |

 

测试输出要求：

本题目的输出要求写入数据库文件夹下的output.txt文件中，例如测试数据库名称为index_test_db，则在测试时使用./bin/rmdb index_test_db命令来启动服务端，对应输出应写入buid/index_test_db/output.txt文件中。


