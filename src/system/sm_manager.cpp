/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#include "sm_manager.h"

#include <sys/stat.h>
#include <unistd.h>

#include <fstream>

#include "index/ix.h"
#include "record/rm.h"
#include "record_printer.h"

/**
 * @description: 判断是否为一个文件夹
 * @return {bool} 返回是否为一个文件夹
 * @param {string&} db_name 数据库文件名称，与文件夹同名
 */
bool SmManager::is_dir(const std::string& db_name) {
    struct stat st;
    return stat(db_name.c_str(), &st) == 0 && S_ISDIR(st.st_mode);
}

/**
 * @description: 创建数据库，所有的数据库相关文件都放在数据库同名文件夹下
 * @param {string&} db_name 数据库名称
 */
void SmManager::create_db(const std::string& db_name) {
    if (is_dir(db_name)) {
        throw DatabaseExistsError(db_name);
    }
    //为数据库创建一个子目录
    std::string cmd = "mkdir " + db_name;
    if (system(cmd.c_str()) < 0) {  // 创建一个名为db_name的目录
        throw UnixError();
    }
    if (chdir(db_name.c_str()) < 0) {  // 进入名为db_name的目录
        throw UnixError();
    }
    //创建系统目录
    DbMeta *new_db = new DbMeta();
    new_db->name_ = db_name;

    // 注意，此处ofstream会在当前目录创建(如果没有此文件先创建)和打开一个名为DB_META_NAME的文件
    std::ofstream ofs(DB_META_NAME);

    // 将new_db中的信息，按照定义好的operator<<操作符，写入到ofs打开的DB_META_NAME文件中
    ofs << *new_db;  // 注意：此处重载了操作符<<

    delete new_db;

    // 创建日志文件
    disk_manager_->create_file(LOG_FILE_NAME);

    // 回到根目录
    if (chdir("..") < 0) {
        throw UnixError();
    }
}

/**
 * @description: 删除数据库，同时需要清空相关文件以及数据库同名文件夹
 * @param {string&} db_name 数据库名称，与文件夹同名
 */
void SmManager::drop_db(const std::string& db_name) {
    if (!is_dir(db_name)) {
        throw DatabaseNotFoundError(db_name);
    }
    std::string cmd = "rm -r " + db_name;
    if (system(cmd.c_str()) < 0) {
        throw UnixError();
    }
}

/**
 * @description: 打开数据库，找到数据库对应的文件夹，并加载数据库元数据和相关文件
 * @param {string&} db_name 数据库名称，与文件夹同名
 */
void SmManager::open_db(const std::string& db_name) {
    if (!is_dir(db_name)) {
		throw DatabaseNotFoundError(db_name);
	}
	if (chdir(db_name.c_str()) < 0) {
		throw UnixError();
	}
	std::ifstream ifs(DB_META_NAME);
	ifs >> db_;
	ifs.close();
	for (auto & [fst, snd] : db_.tabs_) {
		auto & tab = snd;
		fhs_.emplace(tab.name, rm_manager_->open_file(tab.name));
		for (const IndexMeta & index : tab.indexes) {
			auto index_name = ix_manager_->get_index_name(tab.name, index.cols);
			ihs_.emplace(index_name, ix_manager_->open_index(tab.name, index.cols));
		}
	}

}

/**
 * @description: 把数据库相关的元数据刷入磁盘中
 */
void SmManager::flush_meta() {
    // 默认清空文件
    std::ofstream ofs(DB_META_NAME);
    ofs << db_;
}

/**
 * @description: 关闭数据库并把数据落盘
 */
void SmManager::close_db() {
    if (db_.name_.empty()) {
        std::cerr << "No database is currently open!" << std::endl;
        return;
    }

    // std::string meta_file = db_.name_ + ".meta"; // Use DB_META_NAME for consistency
    try {
        std::ofstream ofs(DB_META_NAME, std::ios::out | std::ios::trunc);
        if (!ofs.is_open()) {
            throw std::ios_base::failure("Failed to open metadata file for writing: " + std::string(DB_META_NAME));
        }

        ofs << db_;
        ofs.close();
        // std::cout << "Database metadata saved to " << DB_META_NAME << std::endl; // Optional: can keep or remove this log

        // 关闭所有记录文件句柄（内部已包含缓冲池刷新）
        for (auto& [table_name, fh] : fhs_) {
            rm_manager_->close_file(fh.get());
        }
        fhs_.clear();

        // 关闭所有索引文件句柄（内部已包含缓冲池刷新）
        for (auto& [index_name, ih] : ihs_) {
            ix_manager_->close_index(ih.get());
        }
        ihs_.clear();

        db_ = DbMeta();
        std::cout << "Database closed successfully." << std::endl;
    } catch (const std::exception &e) {
        std::cerr << "Error while closing database: " << e.what() << std::endl;
        throw;
    }
}

/**
 * @description: 显示所有的表,通过测试需要将其结果写入到output.txt,详情看题目文档
 * @param {Context*} context 
 */
void SmManager::show_tables(Context* context) {
    std::fstream outfile;
    outfile.open("output.txt", std::ios::out | std::ios::app);
    outfile << "| Tables |\n";
    RecordPrinter printer(1);
    printer.print_separator(context);
    printer.print_record({"Tables"}, context);
    printer.print_separator(context);
    for (auto &entry : db_.tabs_) {
        auto &tab = entry.second;
        printer.print_record({tab.name}, context);
        outfile << "| " << tab.name << " |\n";
    }
    printer.print_separator(context);
    outfile.close();
}

/**
 * @description: 显示表的元数据
 * @param {string&} tab_name 表名称
 * @param {Context*} context 
 */
void SmManager::desc_table(const std::string& tab_name, Context* context) {
    TabMeta &tab = db_.get_table(tab_name);

    std::vector<std::string> captions = {"Field", "Type", "Index"};
    RecordPrinter printer(captions.size());
    // Print header
    printer.print_separator(context);
    printer.print_record(captions, context);
    printer.print_separator(context);
    // Print fields
    for (auto &col : tab.cols) {
        std::vector<std::string> field_info = {col.name, coltype2str(col.type), col.index ? "YES" : "NO"};
        printer.print_record(field_info, context);
    }
    // Print footer
    printer.print_separator(context);
}

/**
 * @description: 创建表
 * @param {string&} tab_name 表的名称
 * @param {vector<ColDef>&} col_defs 表的字段
 * @param {Context*} context 
 */
void SmManager::create_table(const std::string& tab_name, const std::vector<ColDef>& col_defs, Context* context) {
    if (db_.is_table(tab_name)) {
        throw TableExistsError(tab_name);
    }
    // Create table meta
    int curr_offset = 0;
    TabMeta tab;
    tab.name = tab_name;
    tab.record_count = 0;  // 初始化记录数量为0
    for (auto &col_def : col_defs) {
        ColMeta col = {.tab_name = tab_name,
                       .name = col_def.name,
                       .type = col_def.type,
                       .len = col_def.len,
                       .offset = curr_offset,
                       .index = false};
        curr_offset += col_def.len;
        tab.cols.push_back(col);
    }
    // Create & open record file
    int record_size = curr_offset;  // record_size就是col meta所占的大小（表的元数据也是以记录的形式进行存储的）
    rm_manager_->create_file(tab_name, record_size);
    db_.tabs_[tab_name] = tab;
    // fhs_[tab_name] = rm_manager_->open_file(tab_name);
    fhs_.emplace(tab_name, rm_manager_->open_file(tab_name));

    flush_meta();
}

/**
 * @description: 删除表
 * @param {string&} tab_name 表的名称
 * @param {Context*} context
 */
void SmManager::drop_table(const std::string& tab_name, Context* context) {
    //1.找到相对应的table；
    if(!db_.is_table(tab_name)){
        throw TableNotFoundError(tab_name);
    }

    // 意向写锁
    context->lock_mgr_->lock_exclusive_on_table(context->txn_, fhs_[tab_name]->GetFd());

    //2.找到相对应的meta；
    TabMeta &tab = db_.get_table(tab_name);

    //3.将对应的meta及其文件关闭并destory；
    //删除索引
    for(auto &index: tab.indexes){
        const auto& index_name = ix_manager_->get_index_name(tab_name, index.cols);
        ix_manager_->close_index(ihs_.at(index_name).get());
        ix_manager_->destroy_index(tab_name, index.cols);
        ihs_.erase(index_name);
    }

    //删除表文件
    rm_manager_->close_file(fhs_[tab_name].get());
    rm_manager_->destroy_file(tab_name);

    //删除表的meta
    fhs_.erase(tab_name);
    db_.tabs_.erase(tab_name);

    //确保recovery拿到正确的元数据
    flush_meta();
}

/**
 * @description: 创建索引
 * @param {string&} tab_name 表的名称
 * @param {vector<string>&} col_names 索引包含的字段名称
 * @param {Context*} context
 */
void SmManager::create_index(const std::string& tab_name, const std::vector<std::string>& col_names, Context* context) {
    if (!db_.is_table(tab_name)) throw TableNotFoundError(tab_name);
    if (ix_manager_->exists(tab_name, col_names)) throw IndexExistsError(tab_name, col_names);

    TabMeta& tab = db_.get_table(tab_name);
    IndexMeta index;
    index.tab_name = tab_name;
    index.col_num = col_names.size();
    index.col_tot_len = 0;

    for (const auto& col_name : col_names) {  // 优化列元数据填充
        index.cols.push_back(*tab.get_col(col_name));
        index.col_tot_len += tab.get_col(col_name)->len;
    }
    //创建索引文件
    ix_manager_->create_index(tab_name, index.cols);
    //更新table和db的元数据
    tab.indexes.push_back(index);
    ihs_.emplace(ix_manager_->get_index_name(tab_name, col_names), ix_manager_->open_index(tab_name, col_names));
    //创建索引
    auto &index_handle = ihs_.at(ix_manager_->get_index_name(tab_name, col_names));
    auto *file_handle = fhs_.at(tab_name).get();
    RmScan scan(file_handle);
    if(scan.rid().page_no == -1 && scan.rid().slot_no == -1){ //创建了空索引
        return;
    }
    while(!scan.is_end()){
        auto rec = file_handle->get_record(scan.rid(), context);
        char * key = new char[index.col_tot_len];
        int offset = 0;
        for(int i=0; i<index.col_num; ++i){
            memcpy(key+offset, rec->data + index.cols[i].offset, index.cols[i].len);
            offset += index.cols[i].len;
        }
        index_handle->insert_entry(key, scan.rid(), context->txn_);
        scan.next();
    }
    flush_meta(); // 持久化元数据更改
}

/**
 * @description: 删除索引
 * @param {string&} tab_name 表名称
 * @param {vector<string>&} col_names 索引包含的字段名称
 * @param {Context*} context
 */
void SmManager::drop_index(const std::string& tab_name, const std::vector<std::string>& col_names, Context* context) {
    TabMeta &table_meta = db_.get_table(tab_name);
    if(!db_.is_table(tab_name)){
        throw TableNotFoundError(tab_name);
    }
    if(!table_meta.is_index(col_names)){
        throw IndexNotFoundError(tab_name, col_names);
    }
    auto &index_handler = ihs_.at(ix_manager_->get_index_name(tab_name, col_names));
    auto *file_handler = fhs_.at(tab_name).get();
    IndexMeta index_meta = *table_meta.get_index_meta(col_names);
    RmScan scan(file_handler);

    if(scan.rid().slot_no != -1||scan.rid().page_no != -1 ){ // 空索引无需处理
        while(!scan.is_end()){
            auto record = file_handler->get_record(scan.rid(), context);
            int off = 0;
            char * key = new char[index_meta.col_tot_len];
            for(int i=0; i<index_meta.col_num; ++i){
                memcpy(key+off, record->data + index_meta.cols[i].offset, index_meta.cols[i].len);
                off += index_meta.cols[i].len;
            }
            index_handler->delete_entry(key, context->txn_);// 删除索引项
            scan.next();
        }
    }
    // 清理索引资源
    ix_manager_->close_index(ihs_[ix_manager_->get_index_name(tab_name, col_names)].get());
    ix_manager_->destroy_index(tab_name, col_names);

    // 更新元数据
    table_meta.indexes.erase(table_meta.get_index_meta(col_names));
    ihs_.erase(ix_manager_->get_index_name(tab_name, col_names));
}

/**
 * @description: 删除索引
 * @param {string&} tab_name 表名称
 * @param {vector<ColMeta>&} 索引包含的字段元数据
 * @param {Context*} context
 */
void SmManager::drop_index(const std::string& tab_name, const std::vector<ColMeta>& cols, Context* context) {
    // 提取列名列表
    std::vector<std::string> col_names;
    for (const auto& col : cols) {
        col_names.push_back(col.name); // 假设 ColMeta 中包含列名字段 `name`
    }

    // 调用已有的 drop_index 方法（参数为列名列表）
    drop_index(tab_name, col_names, context);
}

void SmManager::show_index(const std::string &tab_name, Context *context)
{
    std::fstream outfile;
    outfile.open("output.txt", std::ios::out | std::ios::app);

    if (!db_.is_table(tab_name)) { // 表不存在
        if(outfile.is_open()) outfile.close();
        return;
    }
    TabMeta& table_meta = db_.get_table(tab_name);
    if (table_meta.indexes.empty()) { // 没有索引
        if(outfile.is_open()) outfile.close();
        return;
    }

    // For console output via RecordPrinter
    std::vector<std::string> captions = {"Table", "Attribute", "Columns"};
    RecordPrinter printer(captions.size());

    printer.print_separator(context);
    printer.print_record(captions, context);
    printer.print_separator(context);

    size_t index_count = 0;
    for (const auto& index_meta : table_meta.indexes) {
        // Prepare string for console output
        std::string cols_str_console = "(";
        for (int i = 0; i < index_meta.col_num; i++) {
            cols_str_console += index_meta.cols[i].name;
            if (i < index_meta.col_num - 1) {
                cols_str_console += ",";
            }
        }
        cols_str_console += ")";
        
        std::vector<std::string> record_values = {tab_name, "unique", cols_str_console};
        printer.print_record(record_values, context);
        
        // Write to output.txt using the old format
        if (outfile.is_open()) {
            outfile << "| " << tab_name << " | unique | (";
            for (int i = 0; i < index_meta.col_num; i++) {
                outfile << index_meta.cols[i].name;
                if (i < index_meta.col_num - 1) {
                    outfile << ",";
                }
            }
            outfile << ") |\n";
        }
        index_count++;
    }

    printer.print_separator(context);
    RecordPrinter::print_record_count(index_count, context);

    if(outfile.is_open()) {
        outfile.close();
    }
}

void SmManager::rollback_insert(const std::string &tab_name, const Rid &rid, Context *context) {
    auto tab = db_.get_table(tab_name);
    auto record = fhs_.at(tab_name).get()->get_record(rid, context);
    for (auto index : tab.indexes) {
        IxIndexHandle *indexHandle = ihs_.at(get_ix_manager()->get_index_name(tab_name, index.cols)).get();
        for (auto column : index.cols) {
            indexHandle->delete_entry(record->data + column.offset, nullptr);
        }
    }
    fhs_.at(tab_name).get()->delete_record(rid, context);
}

void SmManager::rollback_delete(const std::string &tab_name, const RmRecord &record, Context *context) {
    auto tab = db_.get_table(tab_name);
    auto rid = fhs_.at(tab_name).get()->insert_record(record.data, context);
    for (auto index : tab.indexes) {
        IxIndexHandle *indexHandle = ihs_.at(get_ix_manager()->get_index_name(tab_name, index.cols)).get();
        for (auto column : index.cols) {
            indexHandle->insert_entry(record.data + column.offset, rid, context->txn_);
        }
    }
}

void SmManager::rollback_update(const std::string &tab_name, const Rid &rid, const RmRecord &record, Context *context) {
    auto tab = db_.get_table(tab_name);
    auto currentRecord = fhs_.at(tab_name).get()->get_record(rid, context);
    for (auto index : tab.indexes) {
        IxIndexHandle *indexHandle = ihs_.at(get_ix_manager()->get_index_name(tab_name, index.cols)).get();
        for (auto column : index.cols) {
            indexHandle->delete_entry(currentRecord->data + column.offset, nullptr);
        }
    }
    fhs_.at(tab_name).get()->update_record(rid, record.data, context);

    for (auto index : tab.indexes) {
        IxIndexHandle *indexHandle = ihs_.at(get_ix_manager()->get_index_name(tab_name, index.cols)).get();
        for (auto column : index.cols)
            indexHandle->insert_entry(record.data + column.offset, rid, context->txn_);
    }
}

// 记录数量维护方法实现
void SmManager::increment_record_count(const std::string &tab_name) {
    TabMeta& tab = db_.get_table(tab_name);
    tab.record_count++;
    flush_meta();
}

void SmManager::decrement_record_count(const std::string &tab_name) {
    TabMeta& tab = db_.get_table(tab_name);
    if (tab.record_count > 0) {
        tab.record_count--;
    }
    flush_meta();
}

void SmManager::set_record_count(const std::string &tab_name, size_t count) {
    TabMeta& tab = db_.get_table(tab_name);
    tab.record_count = count;
    flush_meta();
}

size_t SmManager::get_record_count(const std::string &tab_name) {
    TabMeta& tab = db_.get_table(tab_name);
    return tab.record_count;
}