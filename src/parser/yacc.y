%{
#include "ast.h"
#include "yacc.tab.h"
#include <iostream>
#include <memory>

int yylex(YYSTYPE *yylval, YYLTYPE *yylloc);

void yyerror(YYLTYPE *locp, const char* s) {
    std::cerr << "Parser Error at line " << locp->first_line << " column " << locp->first_column << ": " << s << std::endl;
}

using namespace ast;
%}

// request a pure (reentrant) parser
%define api.pure full
// enable location in error handler
%locations
// enable verbose syntax error message
%define parse.error verbose

// keywords
%token SHOW TABLES CREATE TABLE DROP DESC INSERT INTO VALUES DELETE FROM ASC ORDER BY
WHERE UPDATE SET SELECT INT CHAR FLOAT INDEX AND JOIN ON SEMI EXIT HELP EXPLAIN TXN_BEGIN TXN_COMMIT TXN_ABORT TXN_ROLLBACK ORDER_BY ENABLE_NESTLOOP ENABLE_SORTMERGE LEFT RIGHT FULL
// non-keywords
%token LEQ NEQ GEQ T_EOF

// type-specific tokens
%token <sv_str> IDENTIFIER VALUE_STRING
%token <sv_int> VALUE_INT
%token <sv_float> VALUE_FLOAT
%token <sv_bool> VALUE_BOOL

// specify types for non-terminal symbol
%type <sv_node> stmt dbStmt ddl dml txnStmt setStmt
%type <sv_field> field
%type <sv_fields> fieldList
%type <sv_type_len> type
%type <sv_comp_op> op
%type <sv_expr> expr
%type <sv_val> value
%type <sv_vals> valueList
%type <sv_str> tbName colName
%type <sv_strs> colNameList /* Removed tableList */
%type <sv_node> from_clause /* New type for from_clause */
%type <sv_join_type> join_type_specifier opt_join_type
%type <sv_str> opt_alias /* Added type for opt_alias */
%type <sv_col> col
%type <sv_cols> colList selector
%type <sv_set_clause> setClause
%type <sv_set_clauses> setClauses
%type <sv_cond> condition
%type <sv_conds> whereClause optWhereClause opt_on_clause
%type <sv_orderby>  order_clause opt_order_clause
%type <sv_orderby_dir> opt_asc_desc
%type <sv_setKnobType> set_knob_type

%%
start:
        stmt ';'
    {
        parse_tree = $1;
        YYACCEPT;
    }
    |   HELP
    {
        parse_tree = std::make_shared<Help>();
        YYACCEPT;
    }
    |   EXIT
    {
        parse_tree = nullptr;
        YYACCEPT;
    }
    |   T_EOF
    {
        parse_tree = nullptr;
        YYACCEPT;
    }
    ;

stmt:
        dbStmt
    |   ddl
    |   dml
    |   txnStmt
    |   setStmt
    |   EXPLAIN stmt
    {
        $$ = std::make_shared<ExplainStmt>($2);
    }
    ;

txnStmt:
        TXN_BEGIN
    {
        $$ = std::make_shared<TxnBegin>();
    }
    |   TXN_COMMIT
    {
        $$ = std::make_shared<TxnCommit>();
    }
    |   TXN_ABORT
    {
        $$ = std::make_shared<TxnAbort>();
    }
    | TXN_ROLLBACK
    {
        $$ = std::make_shared<TxnRollback>();
    }
    ;

dbStmt:
        SHOW TABLES
    {
        $$ = std::make_shared<ShowTables>();
    }
    ;

setStmt:
        SET set_knob_type '=' VALUE_BOOL
    {
        $$ = std::make_shared<SetStmt>($2, $4);
    }
    ;

ddl:
        CREATE TABLE tbName '(' fieldList ')'
    {
        $$ = std::make_shared<CreateTable>($3, $5);
    }
    |   DROP TABLE tbName
    {
        $$ = std::make_shared<DropTable>($3);
    }
    |   DESC tbName
    {
        $$ = std::make_shared<DescTable>($2);
    }
    |   CREATE INDEX tbName '(' colNameList ')'
    {
        $$ = std::make_shared<CreateIndex>($3, $5);
    }
    |   DROP INDEX tbName '(' colNameList ')'
    {
        $$ = std::make_shared<DropIndex>($3, $5);
    }
    |   SHOW INDEX FROM tbName
    {
        $$ = std::make_shared<ShowIndex>($4);
    }
    ;

dml:
        INSERT INTO tbName VALUES '(' valueList ')'
    {
        $$ = std::make_shared<InsertStmt>($3, $6);
    }
    |   DELETE FROM tbName optWhereClause
    {
        $$ = std::make_shared<DeleteStmt>($3, $4);
    }
    |   UPDATE tbName SET setClauses optWhereClause
    {
        $$ = std::make_shared<UpdateStmt>($2, $4, $5);
    }
    |   SELECT selector FROM from_clause optWhereClause opt_order_clause
    {
        // $2: selector, $4: from_clause (contains tables and joins), $5: where_conditions, $6: order_by
        auto select_node = std::dynamic_pointer_cast<SelectStmt>($4); // from_clause should build a SelectStmt
        if (!select_node) {
            // This case handles simple "SELECT ... FROM table" without explicit JOINs initially
            // It might need adjustment based on how from_clause is structured for single tables
            // For now, assume from_clause always results in a SelectStmt or similar structure
            // that can be adapted.
            // If from_clause directly returns a list of tables for non-join cases,
            // we'd need to construct a SelectStmt here.
            // Let's assume from_clause is designed to always provide a base SelectStmt
            // or a structure that can be cast or converted to it.
            // This part needs careful implementation of from_clause.
            // For simplicity, let's assume from_clause always sets up a basic SelectStmt
            // and subsequent JOINs modify it.
             yyerror(&@$, "Internal parser error: from_clause did not produce a SelectStmt.");
             YYABORT;
        }
        select_node->cols = $2;
        // If optWhereClause ($5) has conditions, they are appended.
        // The SelectStmt from from_clause might already have some conditions (e.g., from ON clauses).
        // We need to merge them or decide on precedence.
        // For now, let's assume optWhereClause conditions are additional.
        if (!$5.empty()) {
            select_node->conds.insert(select_node->conds.end(), $5.begin(), $5.end());
        }
        select_node->order = $6;
        select_node->has_sort = (bool)$6;
        $$ = select_node;
    }
    ;

from_clause:
        tbName opt_alias
    {
        // Base case: a single table reference
        auto select_node = std::make_shared<SelectStmt>(
            std::vector<std::shared_ptr<Col>>{}, // cols will be set by SELECT rule
            std::vector<std::string>{$1},        // initial table
            std::vector<std::shared_ptr<BinaryExpr>>{}, // no initial where conds from here
            nullptr                                     // no initial order by from here
        );
        if (!$2.empty()) { // if alias exists
            select_node->table_aliases[$2] = $1;
            select_node->tabs[0] = $2; // Use alias in tabs list if present
        }
        $$ = select_node;
    }
    |   from_clause ',' tbName opt_alias
    {
        // Handle comma-separated table list for cross join (Cartesian product)
        auto select_node = std::dynamic_pointer_cast<SelectStmt>($1);
        if (!select_node) {
             yyerror(&@$, "Internal parser error: from_clause did not produce a SelectStmt for comma-separated tables.");
             YYABORT;
        }
        std::string table_name = $3;
        std::string table_alias = $4;
        std::string table_ref = table_alias.empty() ? table_name : table_alias;
        
        select_node->tabs.push_back(table_ref); // Add new table (or its alias) to the list of tables
        if (!table_alias.empty()) {
            select_node->table_aliases[table_alias] = table_name;
        }
        $$ = select_node;
    }
    |   from_clause opt_join_type JOIN tbName opt_alias opt_on_clause
    {
        // Recursive case: a JOIN operation
        auto select_node = std::dynamic_pointer_cast<SelectStmt>($1);
        if (!select_node) {
             yyerror(&@$, "Internal parser error: from_clause did not produce a SelectStmt for JOIN.");
             YYABORT;
        }
        std::string right_table_name = $4;
        std::string right_table_alias = $5;
        std::string right_table_ref = right_table_alias.empty() ? right_table_name : right_table_alias;

        // The 'left' part of the join is implicitly what's already in select_node->tabs
        // For simplicity in JoinExpr, we might need to represent the "left" side.
        // This could be the last table added or a conceptual representation of the joined result so far.
        // For now, let's assume JoinExpr's left/right refer to the direct tables being joined in this step.
        // The optimizer will later build the actual join tree.

        // The first table in the select_node->tabs can be considered the "left" for this specific join operation.
        // This is a simplification; a more robust approach might involve tracking the "current" composite table.
        std::string left_table_ref = select_node->tabs.back(); // Last table added is the left side of this new join.

        auto join_expr = std::make_shared<JoinExpr>(left_table_ref, right_table_ref, $6, $2); // $7 changed to $6 for opt_on_clause
        select_node->jointree.push_back(join_expr);
        select_node->tabs.push_back(right_table_ref); // Add new table (or its alias) to the list of tables
        if (!right_table_alias.empty()) {
            select_node->table_aliases[right_table_alias] = right_table_name;
        }
        $$ = select_node;
    }
    ;

opt_alias:
        /* epsilon */ { $$ = ""; }
    |   tbName      { $$ = $1; } // tbName is IDENTIFIER, can serve as alias
    ;

join_type_specifier:
        /* epsilon */ { $$ = ::INNER_JOIN; } // Default to INNER JOIN
    |   SEMI          { $$ = ::SEMI_JOIN; }
    |   LEFT          { $$ = ::LEFT_JOIN; }
    |   RIGHT         { $$ = ::RIGHT_JOIN; }
    |   FULL          { $$ = ::FULL_JOIN; }
    ;

opt_join_type:
        join_type_specifier
    {
        $$ = $1;
    }
    ;

fieldList:
        field
    {
        $$ = std::vector<std::shared_ptr<Field>>{$1};
    }
    |   fieldList ',' field
    {
        $$.push_back($3);
    }
    ;

colNameList:
        colName
    {
        $$ = std::vector<std::string>{$1};
    }
    | colNameList ',' colName
    {
        $$.push_back($3);
    }
    ;

field:
        colName type
    {
        $$ = std::make_shared<ColDef>($1, $2);
    }
    ;

type:
        INT
    {
        $$ = std::make_shared<TypeLen>(SV_TYPE_INT, sizeof(int));
    }
    |   CHAR '(' VALUE_INT ')'
    {
        $$ = std::make_shared<TypeLen>(SV_TYPE_STRING, $3);
    }
    |   FLOAT
    {
        $$ = std::make_shared<TypeLen>(SV_TYPE_FLOAT, sizeof(float));
    }
    ;

valueList:
        value
    {
        $$ = std::vector<std::shared_ptr<Value>>{$1};
    }
    |   valueList ',' value
    {
        $$.push_back($3);
    }
    ;

value:
        VALUE_INT  // $1 is the integer value (from yylval.sv_int)
    {
        // yylval is the ast::SemValue object from yylex, containing sv_raw_text
        $$ = std::make_shared<IntLit>($1, yylval.sv_raw_text);
    }
    |   VALUE_FLOAT // $1 is the float value (from yylval.sv_float)
    {
        $$ = std::make_shared<FloatLit>($1, yylval.sv_raw_text);
    }
    |   VALUE_STRING // $1 is the string content without quotes (from yylval.sv_str)
    {
        // yylval.sv_raw_text contains the original literal with quotes
        $$ = std::make_shared<StringLit>($1, yylval.sv_raw_text);
    }
    |   VALUE_BOOL // $1 is the boolean value (from yylval.sv_bool)
    {
        $$ = std::make_shared<BoolLit>($1);
    }
    ;

condition:
        col op expr
    {
        $$ = std::make_shared<BinaryExpr>($1, $2, $3);
    }
    ;

optWhereClause:
        /* epsilon */ { /* ignore*/ }
    |   WHERE whereClause
    {
        $$ = $2;
    }
    ;

whereClause:
        condition 
    {
        $$ = std::vector<std::shared_ptr<BinaryExpr>>{$1};
    }
    |   whereClause AND condition
    {
        $$.push_back($3);
    }
    ;

opt_on_clause:
        /* epsilon */ { $$ = std::vector<std::shared_ptr<BinaryExpr>>{}; }
    |   ON whereClause { $$ = $2; }
    ;

col:
        tbName '.' colName
    {
        $$ = std::make_shared<Col>($1, $3);
    }
    |   colName
    {
        $$ = std::make_shared<Col>("", $1);
    }
    ;

colList:
        col
    {
        $$ = std::vector<std::shared_ptr<Col>>{$1};
    }
    |   colList ',' col
    {
        $$.push_back($3);
    }
    ;

op:
        '='
    {
        $$ = SV_OP_EQ;
    }
    |   '<'
    {
        $$ = SV_OP_LT;
    }
    |   '>'
    {
        $$ = SV_OP_GT;
    }
    |   NEQ
    {
        $$ = SV_OP_NE;
    }
    |   LEQ
    {
        $$ = SV_OP_LE;
    }
    |   GEQ
    {
        $$ = SV_OP_GE;
    }
    ;

expr:
        value
    {
        $$ = std::static_pointer_cast<Expr>($1);
    }
    |   col
    {
        $$ = std::static_pointer_cast<Expr>($1);
    }
    ;

setClauses:
        setClause
    {
        $$ = std::vector<std::shared_ptr<SetClause>>{$1};
    }
    |   setClauses ',' setClause
    {
        $$.push_back($3);
    }
    ;

setClause:
        colName '=' value
    {
        $$ = std::make_shared<SetClause>($1, $3);
    }
    ;

selector:
        '*'
    {
        $$ = {};
    }
    |   colList
    ;

/* Remove old tableList rules as 'from_clause' handles this now */
/*
tableList:
        tbName
    {
        $$ = std::vector<std::string>{$1};
    }
    |   tbName tbName
    {
        $$ = std::vector<std::string>{$1};
    }
    |   tableList ',' tbName
    {
        $$.push_back($3);
    }
    |   tableList ',' tbName tbName
    {
        $$.push_back($3);
    }
    ;
*/

opt_order_clause:
    ORDER BY order_clause      
    { 
        $$ = $3; 
    }
    |   /* epsilon */ { /* ignore*/ }
    ;

order_clause:
      col  opt_asc_desc 
    { 
        $$ = std::make_shared<OrderBy>($1, $2);
    }
    ;   

opt_asc_desc:
    ASC          { $$ = OrderBy_ASC;     }
    |  DESC      { $$ = OrderBy_DESC;    }
    |       { $$ = OrderBy_DEFAULT; }
    ;    

set_knob_type:
    ENABLE_NESTLOOP { $$ = EnableNestLoop; }
    |   ENABLE_SORTMERGE { $$ = EnableSortMerge; }
    ;

tbName: IDENTIFIER;

colName: IDENTIFIER;
%%
