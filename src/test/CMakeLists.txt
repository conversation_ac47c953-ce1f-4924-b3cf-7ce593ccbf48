# Include CTest module to enable testing with CTest
include(CTest)
add_executable(parser_test parser_test/semi_join_parser_test.cpp)
target_link_libraries(parser_test PRIVATE gtest_main parser)
add_test(NAME ParserSemiJoinTest COMMAND parser_test)

# Analyzer tests
add_executable(analyzer_test analyzer_test/semi_join_analyzer_test.cpp)
target_link_libraries(analyzer_test PRIVATE
    gtest_main
    parser      # For parsing SQL into AST
    analyze     # The library being tested
    system      # For SmManager, DbMeta, etc.
    index       # Dependency of system
    record      # Dependency of system
    storage     # Dependency of index, record
    transaction # For Context, LockManager, Transaction
    recovery    # For Context, LogManager
    # pthread     # Might be needed due to transaction/recovery linking it
)
# Add filesystem for C++17 filesystem features used in test setup/teardown
target_link_libraries(analyzer_test PRIVATE stdc++fs)


add_test(NAME AnalyzerSemiJoinTest COMMAND analyzer_test)

# E2E Query tests for SEMI JOIN
add_executable(e2e_query_test e2e_query_test/semi_join_e2e_test.cpp)
target_link_libraries(e2e_query_test PRIVATE
    gtest_main
    parser      # For parsing SQL into AST
    analyze     # For semantic analysis
    planner     # For Planner (Optimizer uses Planner and is likely header-only or part of other libs)
    execution   # For Portal and executor implementations
    system      # For SmManager, DbMeta, etc.
    index       # Dependency of system/execution
    record      # Dependency of system/execution
    storage     # Dependency of index, record
    transaction # For Context, LockManager, Transaction (used by SmManager, execution context)
    recovery    # For Context, LogManager (used by SmManager, execution context)
    # pthread     # Might be needed due to transaction/recovery linking it
)
# Add filesystem for C++17 filesystem features used in test setup/teardown
target_link_libraries(e2e_query_test PRIVATE stdc++fs)

add_test(NAME E2ESemiJoinTest COMMAND e2e_query_test)

# Test for multi-column index correctness
add_executable(multi_index_test judge_multi_index_test/multi_index_test.cpp)
target_link_libraries(multi_index_test PRIVATE gtest_main)
add_test(NAME MultiIndexCorrectnessTest COMMAND multi_index_test)