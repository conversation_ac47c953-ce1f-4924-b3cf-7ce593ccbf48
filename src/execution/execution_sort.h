/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#pragma once
#include "execution_defs.h"
#include "execution_manager.h"
#include "executor_abstract.h"
#include "index/ix.h"
#include "system/sm.h"

class SortExecutor : public AbstractExecutor {
   private:
    std::unique_ptr<AbstractExecutor> prev_;
    ColMeta cols_;                              // 框架中只支持一个键排序，需要自行修改数据结构支持多个键排序
    size_t tuple_num;
    bool is_desc_;
    std::vector<size_t> used_tuple;
    std::unique_ptr<RmRecord> current_tuple;

   public:
    SortExecutor(std::unique_ptr<AbstractExecutor> prev, TabCol sel_cols, bool is_desc) {
        prev_ = std::move(prev);
        cols_ = prev_->get_col_offset(sel_cols);
        is_desc_ = is_desc;
        tuple_num = 0;
        used_tuple.clear();
    }

    void beginTuple() override { 
        
    }

    void nextTuple() override {
        
    }

    std::unique_ptr<RmRecord> Next() override {
        // TODO: Actual sorting logic and returning sorted tuples.
        return nullptr; // Placeholder
    }

    Rid &rid() override {
        // TODO: Should return RID of the current sorted tuple.
        return _abstract_rid; // Placeholder
    }

    // Returns the schema of the output records.
    // SortExecutor does not change the schema of its child.
    const std::vector<ColMeta>& cols() const override {
        if (!prev_) {
            throw std::runtime_error("SortExecutor has no child executor (prev_ is null).");
        }
        return prev_->cols();
    }

    // TODO: Implement is_end() based on sorted results.
    // bool is_end() const override { return true; } // Placeholder
};