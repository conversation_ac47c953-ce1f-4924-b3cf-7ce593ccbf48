/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#pragma once
#include "execution_defs.h"
#include "execution_manager.h"
#include "executor_abstract.h"
#include "index/ix.h"
#include "system/sm.h"

class InsertExecutor : public AbstractExecutor {
   private:
    TabMeta tab_;                   // 表的元数据
    std::vector<Value> values_;     // 需要插入的数据
    RmFileHandle *fh_;              // 表的数据文件句柄
    std::string tab_name_;          // 表名称
    Rid rid_;                       // 插入的位置，由于系统默认插入时不指定位置，因此当前rid_在插入后才赋值
    SmManager *sm_manager_;
    bool inserted_;                 // Flag to track if the insert operation has been performed

   public:
    InsertExecutor(SmManager *sm_manager, const std::string &tab_name, std::vector<Value> values, Context *context) : inserted_(false) {
        sm_manager_ = sm_manager;
        tab_ = sm_manager_->db_.get_table(tab_name);
        values_ = values;
        tab_name_ = tab_name;
        if (values.size() != tab_.cols.size()) {
            throw InvalidValueCountError();
        }
        fh_ = sm_manager_->fhs_.at(tab_name).get();
        context_ = context;
    };

    std::unique_ptr<RmRecord> Next() override {
        // Make record buffer
        RmRecord rec(fh_->get_file_hdr().record_size);
        for (size_t i = 0; i < values_.size(); i++) {
            auto &col = tab_.cols[i];
            auto &val = values_[i];
            if (col.type != val.type) {
                throw IncompatibleTypeError(coltype2str(col.type), coltype2str(val.type));
            }
            val.init_raw(col.len);
            memcpy(rec.data + col.offset, val.raw->data, col.len);
        }
        
        // Insert into record file
        rid_ = fh_->insert_record(rec.data, context_);
        
        // Insert into index and check for uniqueness constraint violations
        std::vector<std::pair<IxIndexHandle*, char*>> inserted_indexes; // Track successful index insertions for rollback
        
        for(size_t i = 0; i < tab_.indexes.size(); ++i) {
            auto& index = tab_.indexes[i];
            auto ih = sm_manager_->ihs_.at(sm_manager_->get_ix_manager()->get_index_name(tab_name_, index.cols)).get();
            char* key = new char[index.col_tot_len];
            int offset = 0;
            for(size_t j = 0; j < index.col_num; ++j) {
                memcpy(key + offset, rec.data + index.cols[j].offset, index.cols[j].len);
                offset += index.cols[j].len;
            }
            
            // Try to insert into index, check return value
            page_id_t result = ih->insert_entry(key, rid_, context_->txn_);
            if(result == -1) {
                // Index insertion failed (likely due to unique constraint violation)
                delete[] key;
                
                // Rollback: remove record from file and cleanup inserted indexes
                fh_->delete_record(rid_, context_);
                
                // Cleanup successfully inserted index entries
                for(auto& pair : inserted_indexes) {
                    pair.first->delete_entry(pair.second, context_->txn_);
                    delete[] pair.second;
                }
                
                throw RMDBError("Duplicate key violates unique constraint");
            }
            
            // Track successful insertion for potential rollback
            inserted_indexes.push_back(std::make_pair(ih, key));
        }
        
        // Clean up allocated memory for index keys
        for(auto& pair : inserted_indexes) {
            delete[] pair.second;
        }
        
        // 增加记录数量
        sm_manager_->increment_record_count(tab_name_);
        
        inserted_ = true; // Mark as inserted
        return nullptr;   // Insert operation does not return a record
    }

    Rid &rid() override { return rid_; }

    void beginTuple() override {
        // For InsertExecutor, beginTuple might not need to do much.
        // If the executor were designed to be re-runnable for multiple sets of values (not typical for SQL INSERT),
        // then inserted_ would be reset here. For a one-shot SQL INSERT, this is fine.
        // inserted_ = false; // Reset if re-runnable
    }

    bool is_end() const override {
        return inserted_;
    }

    const std::vector<ColMeta>& cols() const override {
        // InsertExecutor does not produce output columns.
        static const std::vector<ColMeta> empty_cols;
        return empty_cols;
    }

    size_t tupleLen() const override {
        // InsertExecutor does not produce output tuples.
        return 0;
    }

    std::string getType() override { return "InsertExecutor"; }
};