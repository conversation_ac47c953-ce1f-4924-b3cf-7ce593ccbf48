/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#pragma once
#include "execution_defs.h"
#include "execution_manager.h"
#include "executor_abstract.h"
#include "index/ix.h"
#include "system/sm.h"

class NestedLoopJoinExecutor : public AbstractExecutor {
   private:
    std::unique_ptr<AbstractExecutor> left_;    // 左儿子节点（需要join的表）
    std::unique_ptr<AbstractExecutor> right_;   // 右儿子节点（需要join的表）
    size_t len_;                                // join后获得的每条记录的长度
    std::vector<ColMeta> cols_;                 // join后获得的记录的字段

    std::vector<Condition> fed_conds_;          // join条件
    bool isend;
    std::unique_ptr<RmRecord> left_record_; // 当前左表记录

    // Added to store the primary table names of the child executors
    std::string left_child_table_name_;
    std::string right_child_table_name_;

   private: // Added private section for helper methods
    template <typename T>
    bool compare_values(const T& lhs_val, const T& rhs_val, CompOp op) const {
        switch (op) {
            case OP_EQ: return lhs_val == rhs_val;
            case OP_NE: return lhs_val != rhs_val;
            case OP_LT: return lhs_val < rhs_val;
            case OP_GT: return lhs_val > rhs_val;
            case OP_LE: return lhs_val <= rhs_val;
            case OP_GE: return lhs_val >= rhs_val;
            default: return false;
        }
    }

    bool compare_result(int cmp_res, CompOp op) const {
        switch (op) {
            case OP_EQ: return cmp_res == 0;
            case OP_NE: return cmp_res != 0;
            case OP_LT: return cmp_res < 0;
            case OP_GT: return cmp_res > 0;
            case OP_LE: return cmp_res <= 0;
            case OP_GE: return cmp_res >= 0;
            default: return false;
        }
    }

   public:
    NestedLoopJoinExecutor(std::unique_ptr<AbstractExecutor> left, std::unique_ptr<AbstractExecutor> right,
                            std::vector<Condition> conds) {
        left_ = std::move(left);
        right_ = std::move(right);
        len_ = left_->tupleLen() + right_->tupleLen();
        cols_ = left_->cols();
        auto right_cols = right_->cols();
        for (auto &col : right_cols) {
            col.offset += left_->tupleLen();
        }
        // 右表的列偏移量需要加上左表的记录长度
        cols_.insert(cols_.end(), right_cols.begin(), right_cols.end());
        isend = false;
        fed_conds_ = std::move(conds);

        // Initialize child table names
        if (left_ && !left_->cols().empty()) {
            left_child_table_name_ = left_->cols()[0].tab_name;
        }
        if (right_ && !right_->cols().empty()) {
            right_child_table_name_ = right_->cols()[0].tab_name;
        }
    }

    void beginTuple() override
    {
        left_->beginTuple();
        right_->beginTuple();
        if (left_->is_end())
        {
            isend = true;
            return;
        }
        left_record_ = left_->Next();

        // 初始化时找到第一个符合条件的记录组合
        find_next_valid_tuple();
    }

    void find_next_valid_tuple()
    {
        while (!left_->is_end())
        {
            while (!right_->is_end())
            {
                auto right_record = right_->Next();
                if (right_record && satisfies_join_conds(left_record_.get(), right_record.get()))
                {
                    // 找到第一个符合条件的记录组合，退出
                    return;
                }
                right_->nextTuple();
            }
            right_->beginTuple(); // 重置右表的迭代器
            left_->nextTuple();   // 移动到左表的下一条记录
            if (!left_->is_end())
            {
                left_record_ = left_->Next(); // 获取左表的新记录
            }
        }
        isend = true; // 如果所有可能的记录组合都已检查完毕
    }

    void nextTuple() override
    {
        right_->nextTuple();
        find_next_valid_tuple();
    }

    std::unique_ptr<RmRecord> Next() override
    {
        while (!is_end())
        {
            auto right_record = right_->Next();
            if (right_record && satisfies_join_conds(left_record_.get(), right_record.get()))
            {
                // 如果满足连接条件，合并记录并返回
                return merge_records(left_record_.get(), right_record.get());
            }
            nextTuple(); // 移动到下一个有效记录组合
        }
        return nullptr;
    }
      bool satisfies_join_conds(const RmRecord *left, const RmRecord *right) const
    {
        // 检查所有连接条件是否都满足
        return std::all_of(fed_conds_.begin(), fed_conds_.end(), [&](const Condition &cond)
                           { return evaluate_cond(left, right, cond); });
    }

    std::unique_ptr<RmRecord> merge_records(const RmRecord *left, const RmRecord *right)
    {
        auto combined = std::make_unique<RmRecord>();
        combined->data = new char[len_];
        std::memcpy(combined->data, left->data, left_->tupleLen());
        std::memcpy(combined->data + left_->tupleLen(), right->data, right_->tupleLen());
        return combined;
    }
    bool evaluate_cond(const RmRecord *left, const RmRecord *right, const Condition &cond) const
    {
        // Ensure iterators are valid before dereferencing.
        // This check might be more robust if done after iterator assignment in constructor,
        // potentially throwing if an iterator couldn't be found.
        // For now, assume they are valid if this function is reached.

        // 获取左侧字段数据
        const RmRecord* lhs_src_record;
        if (cond.lhs->tab_name == left_child_table_name_) {
            lhs_src_record = left;
        } else if (cond.lhs->tab_name == right_child_table_name_) {
            lhs_src_record = right;
        } else {
            // This case should ideally not happen if optimizer and planner are correct
            // Or if table names/aliases are not consistently handled.
            // For now, throw an error or handle as per system's error strategy.
            throw std::runtime_error("LHS column '" + cond.lhs->name + "' with table '" + cond.lhs->tab_name +
                                     "' not found in either child executor's tables ('" +
                                     left_child_table_name_ + "', '" + right_child_table_name_ + "').");
        }
        char *lhs_buf = lhs_src_record->data + cond.lhs->offset;

        char *rhs_buf = nullptr;
        if (!cond.is_rhs_val) // Right hand side is a column
        {
            const RmRecord* rhs_src_record;
            if (cond.rhs->tab_name == left_child_table_name_) {
                rhs_src_record = left;
            } else if (cond.rhs->tab_name == right_child_table_name_) {
                rhs_src_record = right;
            } else {
                 throw std::runtime_error("RHS column '" + cond.rhs->name + "' with table '" + cond.rhs->tab_name +
                                     "' not found in either child executor's tables ('" +
                                     left_child_table_name_ + "', '" + right_child_table_name_ + "').");
            }
            // 获取右侧字段数据
            // The offset for rhs_col from right table needs to be relative to the start of the right record's data.
            rhs_buf = rhs_src_record->data + cond.rhs->offset;
        }
        else // Right hand side is a value
        {
            // For literal values, direct access to cond.rhs_val members is preferred.
            // The use of cond.rhs_val.raw->data was in the original snippet but might be problematic
            // if raw is not consistently populated for all literal types.
            // The switch below handles direct value access.
        }

        // 根据列的类型来比较数据
        switch (cond.lhs->type)
        {
        case TYPE_INT:
        {
            int lhs_value = *reinterpret_cast<int *>(lhs_buf);
            int rhs_value;
            if (cond.is_rhs_val) {
                rhs_value = cond.rhs_val.int_val;
            } else {
                // Ensure rhs_buf is not null if cond.is_rhs_val is false
                if (!rhs_buf) return false; // Or throw error
                rhs_value = *reinterpret_cast<int *>(rhs_buf);
            }
            return compare_values(lhs_value, rhs_value, cond.op);
        }
        case TYPE_FLOAT:
        {
            float lhs_value = *reinterpret_cast<float *>(lhs_buf);
            float rhs_value;
            if (cond.is_rhs_val) {
                rhs_value = cond.rhs_val.float_val;
            } else {
                if (!rhs_buf) return false; // Or throw error
                rhs_value = *reinterpret_cast<float *>(rhs_buf);
            }
            return compare_values(lhs_value, rhs_value, cond.op);
        }
        case TYPE_STRING:
        {
            // 使用 std::string::compare 实现字符串比较
            std::string lhs_value(lhs_buf, strnlen(lhs_buf, cond.lhs->len));
            std::string rhs_value;
            if (cond.is_rhs_val) {
                rhs_value = cond.rhs_val.str_val;
            } else {
                if (!rhs_buf) return false; // Or throw error
                // Ensure cond.rhs is valid before accessing cond.rhs->len
                rhs_value = std::string(rhs_buf, strnlen(rhs_buf, cond.rhs->len));
            }
            return compare_result(lhs_value.compare(rhs_value), cond.op);
        }
        default:
            return false; // 对于未知类型，返回 false
        }
    }
    ColMeta get_col_offset(const TabCol &target) override { return *get_col(cols_, target); }
    size_t tupleLen() const override
    {
        return left_->tupleLen() + right_->tupleLen(); // 返回左右节点的记录长度之和
    }

    const std::vector<ColMeta> &cols() const override
    {
        return cols_; // 直接返回已经合并和调整过的列元数据
    }

    bool is_end() const override
    {
        return isend; // 返回当前联接操作的结束状态
    }
    Rid &rid() override { return _abstract_rid; }
};