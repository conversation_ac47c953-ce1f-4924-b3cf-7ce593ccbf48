/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#pragma once
#include "execution_defs.h"
#include "execution_manager.h"
#include "executor_abstract.h"
#include "index/ix.h"
#include "index/ix_index_handle.h"
#include "system/sm.h"

class UpdateExecutor : public AbstractExecutor {
   private:
    TabMeta tab_;
    std::vector<Condition> conds_;
    RmFileHandle *fh_;
    std::vector<Rid> rids_;
    std::string tab_name_;
    std::vector<SetClause> set_clauses_;
    SmManager *sm_manager_;
    bool updated_; // Flag to track if the update operation has been performed

   public:
    UpdateExecutor(SmManager *sm_manager, const std::string &tab_name, std::vector<SetClause> set_clauses,
                   std::vector<Condition> conds, std::vector<Rid> rids, Context *context) : updated_(false) {
        sm_manager_ = sm_manager;
        tab_name_ = tab_name;
        set_clauses_ = set_clauses;
        tab_ = sm_manager_->db_.get_table(tab_name);
        fh_ = sm_manager_->fhs_.at(tab_name).get();
        conds_ = conds;
        rids_ = rids;
        context_ = context;
    }
    std::unique_ptr<RmRecord> Next() override {
// 锁定整个表以进行独占访问
        if (context_ && context_->lock_mgr_) { // Add null check for lock_mgr_ for testing
            context_->lock_mgr_->lock_exclusive_on_table(context_->txn_, fh_->GetFd());
        }

        // 初始化 set_clauses_ 中的每个值
        for (auto& set_clause : set_clauses_) {
            // 获取列的元数据
            auto column_meta = tab_.get_col(set_clause.lhs.col_name);
            // 初始化 set_clause 的右侧值，分配对应长度的内存
            set_clause.rhs.init_raw(column_meta->len);
        }

        // 遍历所有需要更新的记录ID
        for (auto& rid : rids_) {
            // 获取当前记录
            auto record = fh_->get_record(rid, context_);
            // 创建一个新的记录对象，用于存储更新后的记录
            RmRecord new_record{ record->size };
            // 将原始记录的数据拷贝到新记录中
            memcpy(new_record.data, record->data, record->size);

            // 遍历所有的 set_clauses_ 以应用更新
            for (auto& set_clause : set_clauses_) {
                // 获取列的元数据
                auto col_meta = tab_.get_col(set_clause.lhs.col_name);
                // 获取记录的长度
                int record_len = col_meta->len;
                // 获取记录的偏移量
                int record_offset = col_meta->offset;

                // 检查列的类型并执行相应的转换
                if (col_meta->type == TYPE_INT && set_clause.rhs.type == TYPE_FLOAT) {
                    int int_value = static_cast<int>(set_clause.rhs.float_val);
                    memcpy(new_record.data + record_offset, &int_value, record_len);
                }
                else if (col_meta->type == TYPE_FLOAT && set_clause.rhs.type == TYPE_INT) {
                    float float_value = static_cast<float>(set_clause.rhs.int_val);
                    memcpy(new_record.data + record_offset, &float_value, record_len);
                }
                else if (col_meta->type == TYPE_STRING && set_clause.rhs.type == TYPE_STRING) {
                    if (record_len < static_cast<int>(set_clause.rhs.str_val.size())) {
                        throw StringOverflowError();
                    }
                    memset(new_record.data + record_offset, 0, record_len);
                    memcpy(new_record.data + record_offset, set_clause.rhs.str_val.c_str(), set_clause.rhs.str_val.size());
                }
                else if (col_meta->type == set_clause.rhs.type) {
                    // 如果类型匹配，直接复制数据
                    memcpy(new_record.data + record_offset, set_clause.rhs.raw->data, record_len);
                }
                else {
                    // 处理其他类型转换错误
                    /*throw TypeMismatchError(set_clause.lhs.col_name, col_meta->type, set_clause.rhs.type);*/
                }
            }

            // First, check for any potential unique constraint violations before making any changes.
            for (const auto& index : tab_.indexes) {
                char* new_key = new char[index.col_tot_len];
                int offset = 0;
                for (size_t j = 0; j < index.col_num; ++j) {
                    memcpy(new_key + offset, new_record.data + index.cols[j].offset, index.cols[j].len);
                    offset += index.cols[j].len;
                }

                std::vector<Rid> result_rids;
                auto ih = sm_manager_->ihs_.at(sm_manager_->get_ix_manager()->get_index_name(tab_name_, index.cols)).get();

                // If a record with the new key already exists AND it's not the same record we are updating, it's a violation.
                if (ih->get_value(new_key, &result_rids, context_->txn_) && !result_rids.empty() && result_rids[0] != rid) {
                    delete[] new_key;
                    throw RMDBError("Duplicate key violates unique constraint on update");
                }
                delete[] new_key;
            }

            // All checks passed. Now, update indexes and the record.
            for (const auto& index : tab_.indexes) {
                auto ih = sm_manager_->ihs_.at(sm_manager_->get_ix_manager()->get_index_name(tab_name_, index.cols)).get();
                
                char* old_key = new char[index.col_tot_len];
                char* new_key = new char[index.col_tot_len];
                
                int old_offset = 0;
                int new_offset = 0;

                std::vector<ColType> col_types;
                std::vector<int> col_lens;

                for (size_t j = 0; j < index.col_num; ++j) {
                    memcpy(old_key + old_offset, record->data + index.cols[j].offset, index.cols[j].len);
                    old_offset += index.cols[j].len;
                    
                    memcpy(new_key + new_offset, new_record.data + index.cols[j].offset, index.cols[j].len);
                    new_offset += index.cols[j].len;

                    col_types.push_back(index.cols[j].type);
                    col_lens.push_back(index.cols[j].len);
                }

                // If the key part of the record was not changed, skip index update.
                if (ix_compare(old_key, new_key, col_types, col_lens) != 0) {
                    ih->delete_entry(old_key, context_->txn_);
                    ih->insert_entry(new_key, rid, context_->txn_);
                }
                
                delete[] old_key;
                delete[] new_key;
            }

            // Finally, update the record in the table file.
            fh_->update_record(rid, new_record.data, context_);
        }

        updated_ = true;
        return nullptr;
    }

    Rid &rid() override {
        // UpdateExecutor typically doesn't "point" to a single RID after execution in the same way a scan does.
        // _abstract_rid is not explicitly set with a meaningful value for the collection of updates.
        // Returning it as is, or a default.
        return _abstract_rid;
    }

    void beginTuple() override {
        // For UpdateExecutor, beginTuple might not need to do much.
        // Reset the updated_ flag if it were to support multiple executions.
        // updated_ = false; // Reset if re-runnable, but typical UPDATE is one-shot.
    }

    bool is_end() const override {
        return updated_;
    }

    const std::vector<ColMeta>& cols() const override {
        // UpdateExecutor does not produce output columns.
        static const std::vector<ColMeta> empty_cols;
        return empty_cols;
    }

    size_t tupleLen() const override {
        // UpdateExecutor does not produce output tuples.
        return 0;
    }

    std::string getType() override { return "UpdateExecutor"; }
};