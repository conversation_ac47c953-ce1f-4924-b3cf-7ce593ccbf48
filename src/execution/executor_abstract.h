/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#pragma once

#include "execution_defs.h"
#include "common/common.h"
#include "index/ix.h"
#include "system/sm.h"

// NOTE: 算子的抽象类（自己加了注释）
class AbstractExecutor
{
public:
    // 存储记录的唯一标识符
    Rid _abstract_rid;

    // 上下文指针
    Context *context_;

    // 虚析构函数，确保子类可以正确析构
    virtual ~AbstractExecutor() = default;

    // 获取元组长度，默认返回0
    virtual size_t tupleLen() const { throw std::logic_error("tupleLen() should be overridden in derived class"); return 0; };

    // 获取列元数据信息，默认返回空指针（注意：这里有潜在的空指针解引用问题）
    virtual const std::vector<ColMeta> &cols() const
    {
        throw std::logic_error("cols() should be overridden in derived class");
        // The following lines are now unreachable but kept for structure, though a direct throw is cleaner.
        // std::vector<ColMeta> *_cols = nullptr;
        // return *_cols;
    };

    // 获取执行器类型，默认返回"AbstractExecutor"
    virtual std::string getType() { return "AbstractExecutor"; };

    // 开始处理元组的操作，默认不执行任何操作
    virtual void beginTuple() { throw std::logic_error("beginTuple() should be overridden in derived class"); };

    // 处理下一个元组的操作，默认不执行任何操作
    virtual void nextTuple() { throw std::logic_error("nextTuple() should be overridden in derived class"); };

    // 判断是否处理完所有元组，默认返回true
    virtual bool is_end() const { throw std::logic_error("is_end() should be overridden in derived class"); return true; };

    virtual std::vector<Value> constructVal() { throw std::logic_error("constructVal() should be overridden in derived class"); }

    // 获取记录的唯一标识符，纯虚函数，需由子类实现
    virtual Rid &rid() = 0;

    // virtual std::vector<AggrResult> aggregate() {};

    // 获取下一条记录，纯虚函数，需由子类实现
    virtual std::unique_ptr<RmRecord> Next() = 0;

    // 获取列的偏移信息，默认返回空的列元数据
    virtual ColMeta get_col_offset(const TabCol &target) { throw std::logic_error("get_col_offset() should be overridden in derived class"); return ColMeta(); };

    // 获取指定列的迭代器，如果找不到列则抛出异常
    /**
     * 根据目标列的信息，在给定的列元数据向量中查找对应的列元数据迭代器。
     *
     * @param rec_cols 列元数据的向数组，包含了所有的列信息。
     * @param target 待查找的目标列，由表名和列名唯一标识。
     * @return 返回找到的目标列元数据的常量迭代器。
     * @throws ColumnNotFoundError 如果未找到目标列，则抛出列未找到异常。
     */
    std::vector<ColMeta>::const_iterator get_col(const std::vector<ColMeta> &rec_cols, const TabCol &target)
    {
        
        // 使用标准库函数find_if查找目标列元数据
        auto pos = std::find_if(rec_cols.begin(), rec_cols.end(), [&](const ColMeta &col)
                                {
        // 判断当前列元数据是否与目标列匹配
        return col.tab_name == target.tab_name && col.name == target.col_name; });
        // 如果未找到目标列，抛出异常
        if (pos == rec_cols.end())
        {
            throw ColumnNotFoundError(target.tab_name + '.' + target.col_name);
        }
        // 返回找到的目标列元数据的迭代器
        return pos;
    }
    
    // 虚函数：获取表格名称
    // 该函数的目的是提供一个接口，用于返回当前对象关联的表格名称
    // 由于该函数是虚拟的，允许派生类根据具体需求重写该函数，以返回适当的表格名称
    // 返回值：一个标准字符串，表示表格名称
    virtual std::string get_tab_name() {
        throw std::logic_error("get_tab_name() should be overridden in derived class");
        std::string tab_name; // Unreachable
        return tab_name;     // Unreachable
    }

    // 虚函数，用于获取某些长度，具体实现应由派生类提供
    virtual size_t get_len() {
        throw std::logic_error("get_len() should be overridden in derived class");
        // 返回-1表示长度未知或不适用，具体实现应根据派生类的实际情况返回正确值
        return -1; // Unreachable
    }

    // 虚函数，用于获取函数名称列表的引用，具体实现应由派生类提供
    virtual const std::vector<std::string> &get_func_names() const {
        throw std::logic_error("get_func_names() should be overridden in derived class");
        // std::vector<std::string>  *_names = nullptr; // Unreachable
        // return *_names; // Unreachable
    };

    // 虚函数，用于获取新的名称列表的引用，具体实现应由派生类提供
    virtual const std::vector<std::string> &get_new_names() const {
        throw std::logic_error("get_new_names() should be overridden in derived class");
        // std::vector<std::string> *_names = nullptr; // Unreachable
        // return *_names; // Unreachable
    };

    // 虚函数，用于获取选择操作的数量，具体实现应由派生类提供
    virtual const size_t &get_sel_num() const {
        throw std::logic_error("get_sel_num() should be overridden in derived class");
        // 返回0表示数量未知或不适用，具体实现应根据派生类的实际情况返回正确值
        // static const size_t temp = 0; return temp; // Would be one way if not throwing
    };

    // 虚函数，用于获取聚合操作的数量，具体实现应由派生类提供
    virtual const size_t &get_agg_num() const {
        throw std::logic_error("get_agg_num() should be overridden in derived class");
        // 返回0表示数量未知或不适用，具体实现应根据派生类的实际情况返回正确值
        // static const size_t temp = 0; return temp; // Would be one way if not throwing
    };

    // 虚函数，用于获取分组列的元数据，具体实现应由派生类提供
    virtual const ColMeta &get_group_col() const {
        throw std::logic_error("get_group_col() should be overridden in derived class");
        // ColMeta *_col = nullptr; // Unreachable
        // return *_col; // Unreachable
    };

    // 虚函数，用于获取分组标志，具体实现应由派生类提供
    virtual const int &get_group_flag() const {
        throw std::logic_error("get_group_flag() should be overridden in derived class");
        // 返回0表示标志未知或不适用，具体实现应根据派生类的实际情况返回正确值
        // static const int temp = 0; return temp; // Would be one way if not throwing
    };

    // 虚函数，用于获取条件名称列表，具体实现应由派生类提供
    virtual const std::vector<std::string> get_conds_names() const {
        throw std::logic_error("get_conds_names() should be overridden in derived class");
        // std::vector<std::string> *_names = nullptr; // Unreachable
        // return *_names; // Unreachable
    }
    // 虚函数，用于获取比较操作符列表，具体实现应由派生类提供
    virtual const std::vector<CompOp> get_op_list() const {
        throw std::logic_error("get_op_list() should be overridden in derived class");
        // std::vector<CompOp> *_lists = nullptr; // Unreachable
        // return *_lists; // Unreachable
    }
    // 虚函数，用于获取结果值列表，具体实现应由派生类提供
    virtual const std::vector<int> get_rv_list() const {
        throw std::logic_error("get_rv_list() should be overridden in derived class");
        // std::vector<int> *_lists = nullptr; // Unreachable
        // return *_lists; // Unreachable
    }

    virtual const std::vector<Condition> get_conds() const { throw std::logic_error("get_conds() should be overridden in derived class"); }
};