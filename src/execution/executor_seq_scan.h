/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#pragma once

#include "execution_defs.h"
#include "execution_manager.h"
#include "executor_abstract.h"
#include "index/ix.h"
#include "system/sm.h"

class SeqScanExecutor : public AbstractExecutor
{
private:
    std::string tab_name_;             // 表的名称
    std::vector<Condition> conds_;     // scan的条件
    RmFileHandle *fh_;                 // 表的数据文件句柄
    std::vector<ColMeta> cols_;        // scan后生成的该记录的字段元数据数组
    size_t len_;                       // scan后生成的每条记录的长度
    std::vector<Condition> fed_conds_; // 同conds_，两个字段相同

    Rid rid_;
    std::unique_ptr<RecScan> scan_; // table_iterator

    SmManager *sm_manager_;

    // bool table_locked;

public:
    SeqScanExecutor(SmManager *sm_manager, std::string tab_name,
                    std::vector<Condition> conds, Context *context)
    {
        std::this_thread::sleep_for(std::chrono::milliseconds(50));
        // std::cout << "Creating SeqScanExecutor" << std::flush;
        // std::cout << std::endl;
        sm_manager_ = sm_manager;
        tab_name_ = std::move(tab_name);
        conds_ = std::move(conds);
        TabMeta &tab = sm_manager_->db_.get_table(tab_name_);
        fh_ = sm_manager_->fhs_.at(tab_name_).get();
        cols_ = tab.cols;
        len_ = cols_.back().offset + cols_.back().len;

        context_ = context;

        fed_conds_ = conds_;
    }

    // 判断当前record是否满足SQL中condition的要求
    bool handleCondition(std::vector<ColMeta> &cols, Condition &cond, char *data) const
    {
        // 通过列名在 cols 中查找 左边的字段 的元数据信息
        auto lhs_col_meta = *std::find_if(cols.begin(), cols.end(), [&](const ColMeta &col_meta)
                                          { return col_meta.name == cond.lhs_col.col_name; });

        // 计算左值数据在记录中的位置
        const char *lhs_data = data + lhs_col_meta.offset;
        const char *rhs_data = nullptr;

        // 声明cond右边的值/字段
        Value rhs_val;
        ColMeta rhs_col_meta;

        // 如果 cond 的右边是 值
        if (cond.is_rhs_val)
        {
            rhs_val = cond.rhs_val;

            // 检查左右值类型是否兼容
            if ((lhs_col_meta.type == TYPE_STRING && rhs_val.type != TYPE_STRING) ||
                (lhs_col_meta.type != TYPE_STRING && rhs_val.type == TYPE_STRING))
            {
                throw IncompatibleTypeError(lhs_col_meta.name, "rhs_val");
            }
        }
        // 如果 cond 的右边是 字段
        else
        {
            rhs_col_meta = *std::find_if(cols.begin(), cols.end(), [&](ColMeta &col_meta)
                                         { return col_meta.name == cond.rhs_col.col_name; });
            rhs_data = data + rhs_col_meta.offset;

            // 检查左边字段和右边值类型是否兼容
            if ((lhs_col_meta.type == TYPE_STRING && rhs_col_meta.type != TYPE_STRING) ||
                (lhs_col_meta.type != TYPE_STRING && rhs_col_meta.type == TYPE_STRING))
            {
                throw IncompatibleTypeError(lhs_col_meta.name, rhs_col_meta.name);
            }
        }

        std::string str(lhs_data, lhs_col_meta.len);

        auto comparator = [lhs_data, lhs_col_meta, rhs_data, rhs_col_meta, &cond](const Value &rhs_val, ColType lhs_type, CompOp op) -> bool
        {
            switch (lhs_type)
            {
            case TYPE_INT:
                switch (op)
                {
                case OP_EQ:
                    return cond.is_rhs_val ? (float)*(int *)(lhs_data) == rhs_val.int_val
                                           : (float)*(int *)(lhs_data) == (float)*(int *)(rhs_data);
                    break;
                case OP_NE:
                    return cond.is_rhs_val ? (float)*(int *)(lhs_data) != rhs_val.int_val
                                           : (float)*(int *)(lhs_data) != (float)*(int *)(rhs_data);
                    break;
                case OP_LT:
                    return cond.is_rhs_val ? (float)*(int *)(lhs_data) < rhs_val.int_val
                                           : (float)*(int *)(lhs_data) < (float)*(int *)(rhs_data);
                case OP_LE:
                    return cond.is_rhs_val ? (float)*(int *)(lhs_data) <= rhs_val.int_val
                                           : (float)*(int *)(lhs_data) <= (float)*(int *)(rhs_data);
                case OP_GT:
                    return cond.is_rhs_val ? (float)*(int *)(lhs_data) > rhs_val.int_val
                                           : (float)*(int *)(lhs_data) > (float)*(int *)(rhs_data);
                case OP_GE:
                    return cond.is_rhs_val ? (float)*(int *)(lhs_data) >= rhs_val.int_val
                                           : (float)*(int *)(lhs_data) >= (float)*(int *)(rhs_data);
                }
            case TYPE_FLOAT:
                switch (op)
                {
                case OP_EQ:
                    return cond.is_rhs_val ? *(float *)(lhs_data) == rhs_val.float_val
                                           : *(float *)(lhs_data) == *(float *)(rhs_data);
                    break;
                case OP_NE:
                    return cond.is_rhs_val ? *(float *)(lhs_data) != rhs_val.float_val
                                           : *(float *)(lhs_data) != *(float *)(rhs_data);
                case OP_LT:
                    return cond.is_rhs_val ? *(float *)(lhs_data) < rhs_val.float_val
                                           : *(float *)(lhs_data) < *(float *)(rhs_data);
                case OP_LE:
                    return cond.is_rhs_val ? *(float *)(lhs_data) <= rhs_val.float_val
                                           : *(float *)(lhs_data) <= *(float *)(rhs_data);
                case OP_GT:
                    return cond.is_rhs_val ? *(float *)(lhs_data) > rhs_val.float_val
                                           : *(float *)(lhs_data) > *(float *)(rhs_data);
                case OP_GE:
                    return cond.is_rhs_val ? *(float *)(lhs_data) >= rhs_val.float_val
                                           : *(float *)(lhs_data) >= *(float *)(rhs_data);
                }
            case TYPE_STRING:
                switch (op)
                {
                case OP_EQ:
                    return cond.is_rhs_val ? strncmp(lhs_data,
                                                     rhs_val.str_val.c_str(),
                                                     std::max(lhs_col_meta.len, (int)rhs_val.str_val.size())) == 0
                                           : strncmp(lhs_data, rhs_data, std::max(lhs_col_meta.len, rhs_col_meta.len)) == 0;
                case OP_NE:
                    return cond.is_rhs_val ? strncmp(lhs_data,
                                                     rhs_val.str_val.c_str(),
                                                     std::max(lhs_col_meta.len, (int)rhs_val.str_val.size())) != 0
                                           : strncmp(lhs_data, rhs_data, std::max(lhs_col_meta.len, rhs_col_meta.len)) != 0;
                case OP_LT:
                    return cond.is_rhs_val ? strncmp(lhs_data,
                                                     rhs_val.str_val.c_str(),
                                                     std::max(lhs_col_meta.len, (int)rhs_val.str_val.size())) < 0
                                           : strncmp(lhs_data, rhs_data, std::max(lhs_col_meta.len, rhs_col_meta.len)) < 0;
                case OP_LE:
                    return cond.is_rhs_val ? strncmp(lhs_data,
                                                     rhs_val.str_val.c_str(),
                                                     std::max(lhs_col_meta.len, (int)rhs_val.str_val.size())) <= 0
                                           : strncmp(lhs_data, rhs_data, std::max(lhs_col_meta.len, rhs_col_meta.len)) <= 0;
                case OP_GT:
                    return cond.is_rhs_val ? strncmp(lhs_data,
                                                     rhs_val.str_val.c_str(),
                                                     std::max(lhs_col_meta.len, (int)rhs_val.str_val.size())) > 0
                                           : strncmp(lhs_data, rhs_data, std::max(lhs_col_meta.len, rhs_col_meta.len)) > 0;
                case OP_GE:
                    return cond.is_rhs_val ? strncmp(lhs_data, rhs_val.str_val.c_str(),
                                                     std::max(lhs_col_meta.len, (int)rhs_val.str_val.size())) >= 0
                                           : strncmp(lhs_data, rhs_data, std::max(lhs_col_meta.len, rhs_col_meta.len)) >= 0;
                }; // End of switch(op) for TYPE_STRING
                // No break needed here as all inner paths should return.
            default:
                // Handle unsupported types to prevent control reaching end of non-void lambda
                throw InternalError("Unsupported column type in SeqScanExecutor condition evaluation: " + std::to_string(lhs_type));
            } // End of switch (lhs_type)
        }; // End of lambda comparator

        return comparator(rhs_val, lhs_col_meta.type, cond.op);
    }

    // 用于初始化扫描过程，设置初始状态，并找到第一个满足条件的记录。
    void
    beginTuple() override
    {
        /**
         * 顺序扫描加锁算法：
         * (Original locking logic L198-L232 is preserved)
         */
        TabMeta tab_meta = sm_manager_->db_.get_table(tab_name_);
        if (tab_meta.indexes.empty() || conds_.size() == 0)
        {
            context_->lock_mgr_->lock_shared_on_table(context_->txn_, sm_manager_->fhs_.at(tab_name_)->GetFd());
        }
        else
        {
            // Original gap lock logic placeholder
        }

        scan_ = std::make_unique<RmScan>(fh_); // RmScan constructor calls its own next()
                                             // to position at the first physical record or set is_end().
        
        // Loop to find the first record that satisfies conditions.
        while (true) {
            if (scan_->is_end()) {
                // No physical records found from the beginning, or all have been checked.
                // is_end() will correctly return true. rid_ is not updated to an invalid state.
                return;
            }

            // Scan is not at the end, so its current rid is a candidate.
            rid_ = scan_->rid(); // Update our rid_

            bool fit_cond = true;
            std::unique_ptr<RmRecord> rm_record(fh_->get_record(rid_, context_));
            
            if (!rm_record) {
                // Unexpected: scan_ is not end, but get_record failed for its current rid.
                // This could be due to a deleted record. Try to advance scan_ and continue.
                scan_->next(); // Advance to the next physical record
                continue;       // Re-evaluate from the top of the loop
            }

            char *data = rm_record->data;
            for (auto &cond : conds_) {
                if (!handleCondition(cols_, cond, data)) {
                    fit_cond = false;
                    break;
                }
            }

            if (fit_cond) {
                // Found the first record satisfying conditions.
                // rid_ is correctly set. is_end() will be false.
                // Locking for the specific record (if any) would go here.
                // context_->lock_mgr_->lock_shared_on_record(context_->txn_, rid_, fh_->GetFd());
                return;
            }

            // Condition not met, advance scan_ to the next physical record and try again.
            scan_->next();
        }
    }
    // 用于在扫描过程中继续扫描表中的下一个记录，并找到下一个满足条件的记录。
    void nextTuple() override
    {
        if (is_end()) { // If scan already ended (e.g. from beginTuple or previous nextTuple), do nothing
            return;
        }

        while (true) {
            scan_->next(); // Advance the underlying scan to the next physical record

            if (scan_->is_end()) {
                // Scan reached the end. rid_ should not be updated with an invalid scan_->rid().
                // The is_end() method (which calls scan_->is_end()) will now correctly report true.
                return;
            }

            // Scan is not at the end, so update our rid_ to this potential candidate
            rid_ = scan_->rid();

            bool fit_cond = true;
            std::unique_ptr<RmRecord> rm_record(fh_->get_record(rid_, context_));

            if (!rm_record) {
                // This is unexpected if scan_ is not at end and rid_ came from scan_->rid().
                // Could be a deleted record or other inconsistency.
                // To avoid potential infinite loops if scan_->next() doesn't progress past this,
                // we will continue, which will call scan_->next() again.
                // If this state persists, it indicates a deeper issue.
                // For now, the loop structure handles advancing scan_.
                continue;
            }

            char *data = rm_record->data;
            for (auto &cond : conds_) {
                if (!handleCondition(cols_, cond, data)) {
                    fit_cond = false;
                    break;
                }
            }

            if (fit_cond) {
                // Found a record that satisfies conditions. rid_ is now set to it.
                // is_end() will be false (as scan_->is_end() was false).
                return;
            }
            // Condition not met, loop will call scan_->next() again at the beginning of the next iteration.
        }
    }

    /**
     * 从文件中获取下一条记录。
     * 该函数重写了基类的Next()方法，用于在文件中顺序读取并返回下一条记录。
     *
     * @return std::unique_ptr<RmRecord> 返回一个指向读取的记录的智能指针。
     */
    std::unique_ptr<RmRecord> Next() override
    {
        if (is_end()) { // Check if the scan has reached the end (is_end() calls scan_->is_end())
            return nullptr;
        }

        // std::cout<<"???"<<getType()<<std::flush;
        //  通过文件句柄和当前记录ID获取文件中的下一条记录
        // At this point, rid_ should be pointing to a valid record found by beginTuple() or nextTuple()
        std::unique_ptr<RmRecord> raw(fh_->get_record(rid_, context_));

        if (!raw) {
            // This can happen if the record was deleted after scan_ found its Rid,
            // or if rid_ is somehow invalid despite is_end() being false.
            // To prevent further issues, treat this as if the scan ended.
            // Note: A more robust way might be to have RmScan provide a method to force its end state.
            // For now, returning nullptr is the immediate action. The caller should handle it.
            // If is_end() relies on scan_, and scan_ didn't advance, this might lead to repeated calls.
            // However, the primary bug is rid_ being set incorrectly when scan_ *does* end.
            return nullptr;
        }

        // 分配内存以存储解析后的数据
        char *data = new char[len_];

        // 初始化数据内存块为0
        memset(data, 0, len_);

        // 遍历列定义，将每列数据从原始记录复制到新分配的内存中
        size_t offset = 0;
        for (auto &col : cols_)
        {
            memcpy(data + offset, raw->data + col.offset, col.len);
            offset += col.len;
        }

        // 断言确保所有列数据都已正确复制，内存布局与原始记录匹配
        assert(offset == len_);

        // 创建并返回一个新的RmRecord对象，使用新分配的内存存储数据
        std::unique_ptr<RmRecord> record = std::make_unique<RmRecord>(len_, data);

        // 由于record对象已接管数据指针，原始分配的内存可以释放
        delete[] data;

        return record;
    }

    /**
     * 根据预定义的列信息和数据缓冲区，构造并返回一个包含多个Value对象的向量。
     * 每个Value对象代表一行数据中的一列，Value对象的类型根据列的类型来确定。
     *
     * @return 返回一个包含根据列信息构造的Value对象的向量。
     */
    // std::vector<Value> constructVal() override
    // {
    //     // 分配足够空间的缓冲区，用于存储所有列的数据。
    //     char *buf = new char[len_ + 1];

    //     memcpy(buf, fh_->fetch_page_handle(rid_.page_no).get_slot(rid_.slot_no), len_);

    //     Value val;              // 用于临时存储每列数据的Value对象。
    //     std::vector<Value> vec; // 存储最终构造的Value对象数组。
    //     // 遍历每一列，分别处理。
    //     for (const auto &col : cols_)
    //     {
    //         // 为当前列分配空间，并复制数据到该空间。
    //         char dest[col.len + 1];
    //         memcpy(dest, buf + col.offset, col.len);
    //         dest[col.len] = '\0'; // 确保字符串以空字符结尾。

    //         // 根据当前列的类型，设置Value对象的值。
    //         val.type = col.type;
    //         switch (col.type)
    //         {
    //         case TYPE_INT:
    //             val.set_int(*(int *)dest);
    //             break;
    //         case TYPE_FLOAT:
    //             val.set_float(*(float *)dest);
    //             break;
    //         case TYPE_STRING:
    //             val.set_str(dest);
    //             break;
    //         }
    //         // 将处理好的Value对象添加到结果向量中。
    //         vec.emplace_back(val);
    //     }
    //     // 释放之前分配的缓冲区。
    //     delete[] buf;
    //     return vec; // 返回构造好的Value对象数组。
    // }

    Rid &rid() override { return rid_; }

    bool is_end() const override { return scan_->is_end(); }

    const std::vector<ColMeta> &cols() const override { return cols_; }

    ColMeta get_col_offset(const TabCol &target) override { return *get_col(cols_, target); }

    size_t tupleLen() const override { return len_; }

    std::string getType() override { return "SeqScanExecutor"; }

    // const std::vector<Condition> get_conds() const override { return conds_; }
};
