/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#include "query_optimizer.h"
#include "plan_printer.h"
#include <algorithm>
#include <set>

std::shared_ptr<Plan> QueryOptimizer::optimize(std::shared_ptr<Query> query) {
    if (!query) return nullptr;
    
    // 1. 连接顺序优化（如果是多表查询）
    std::shared_ptr<Plan> plan;
    if (query->tables.size() == 1) {
        // 单表查询 - 将查询条件 query->conds 传递给 generate_scan_plan
        plan = generate_scan_plan(query->tables[0], query->conds);
        
        // 条件已经由 generate_scan_plan 生成的扫描节点（SeqScan 或 IndexScan）处理。
        // 此处显然就不符合explain关卡的要求（Filter）
        // 目前，为了让优化器能使用索引，优先保证条件能到达 generate_scan_plan。
    } else {
        // 多表查询 - 应用连接顺序优化
        // 合并WHERE条件和JOIN条件
        std::vector<Condition> all_conds = query->conds;
        all_conds.insert(all_conds.end(), query->join_conds.begin(), query->join_conds.end());
        plan = optimize_join_order(query->tables, all_conds);
        
        // 2. 选择运算下推（仅对多表查询）
        std::vector<Condition> selection_conds = query->conds;
        plan = pushdown_selection(plan, selection_conds);
    }
    
    return plan;
}

std::shared_ptr<Plan> QueryOptimizer::generate_scan_plan(const std::string& table_name, const std::vector<Condition>& conds) {
    // 提取适用于当前表的过滤条件
    std::vector<Condition> table_filters;
    for (const auto& cond : conds) {
        if (cond.lhs_col.tab_name == table_name && cond.is_rhs_val) {
            table_filters.push_back(cond);
        }
    }

    TabMeta& tab_meta = sm_manager_->db_.get_table(table_name);
    const IndexMeta* best_index_meta = nullptr;
    int max_matched_prefix_len = 0;

    if (!tab_meta.indexes.empty()) {
        for (const auto& current_index_meta : tab_meta.indexes) {
            int current_prefix_len = 0;
            bool range_query_on_this_index = false;
            for (const auto& index_col : current_index_meta.cols) { // Iterate through columns of the current index
                bool matched_this_index_col = false;
                for (const auto& filter : table_filters) {
                    if (filter.lhs_col.col_name == index_col.name) { // Condition applies to this index column
                        if (filter.op == OP_EQ) {
                            current_prefix_len++;
                            matched_this_index_col = true;
                            break; // Found an EQ match for this index column
                        } else if (filter.op != OP_NE) { // Range query (GT, LT, GE, LE)
                            current_prefix_len++;
                            matched_this_index_col = true;
                            range_query_on_this_index = true;
                            break; // Found a range match
                        }
                    }
                }
                if (!matched_this_index_col || range_query_on_this_index) {
                    break; // Stop if this index column wasn't matched, or if a range query ended its usefulness for prefix
                }
            }

            if (current_prefix_len > max_matched_prefix_len) {
                max_matched_prefix_len = current_prefix_len;
                best_index_meta = &current_index_meta;
            }
            // Basic tie-breaking: if prefix lengths are equal, prefer shorter indexes (fewer total columns)
            // or could add other heuristics like index type if available.
            else if (current_prefix_len == max_matched_prefix_len && current_prefix_len > 0 && best_index_meta != nullptr) {
                if (current_index_meta.cols.size() < best_index_meta->cols.size()) {
                    best_index_meta = &current_index_meta;
                }
            }
        }
    }

    std::shared_ptr<Plan> scan_plan;
    if (max_matched_prefix_len > 0 && best_index_meta != nullptr) {
        std::vector<std::string> full_index_col_names;
        for (const auto& col : best_index_meta->cols) {
            full_index_col_names.push_back(col.name);
        }
        // Pass all table_filters; the IndexScanExecutor will use the ones relevant to the chosen index prefix.
        scan_plan = std::make_shared<ScanPlan>(T_IndexScan, sm_manager_, table_name, table_filters, full_index_col_names);
    } else {
        std::vector<std::string> empty_index_cols; // For SeqScan, index_cols is empty
        scan_plan = std::make_shared<ScanPlan>(T_SeqScan, sm_manager_, table_name, table_filters, empty_index_cols);
    }
    
    return scan_plan;
}

std::shared_ptr<Plan> QueryOptimizer::optimize_join_order(const std::vector<std::string>& tables, std::vector<Condition>& join_conds) {
    if (tables.size() < 2) {
        return nullptr;
    }
    
    // 使用贪心算法选择连接顺序
    std::vector<std::pair<std::string, size_t>> table_cardinalities;
    for (const auto& table : tables) {
        size_t cardinality = get_table_cardinality(table);
        table_cardinalities.push_back({table, cardinality});
    }
    
    // 按基数排序
    std::sort(table_cardinalities.begin(), table_cardinalities.end(),
              [](const auto& a, const auto& b) { return a.second < b.second; });
    
    // 生成左深树结构
    std::shared_ptr<Plan> result_plan;
    std::set<std::string> joined_tables;
    
    // 首先连接基数最小的两个表
    if (table_cardinalities.size() >= 2) {
        std::string left_table = table_cardinalities[0].first;
        std::string right_table = table_cardinalities[1].first;
        
        // 为左表和右表生成扫描计划（不包含条件，让pushdown_selection处理）
        auto left_plan = generate_scan_plan(left_table, {});
        auto right_plan = generate_scan_plan(right_table, {});
        
        // 寻找连接条件
        std::vector<Condition> join_conditions;
        auto it = join_conds.begin();
        while (it != join_conds.end()) {
            if (!it->is_rhs_val && 
                ((it->lhs_col.tab_name == left_table && it->rhs_col.tab_name == right_table) ||
                 (it->lhs_col.tab_name == right_table && it->rhs_col.tab_name == left_table))) {
                join_conditions.push_back(*it);
                it = join_conds.erase(it);
            } else {
                ++it;
            }
        }
        
        result_plan = std::make_shared<JoinPlan>(T_NestLoop, left_plan, right_plan, join_conditions);
        joined_tables.insert(left_table);
        joined_tables.insert(right_table);
    }
    
    // 依次加入剩余的表
    for (size_t i = 2; i < table_cardinalities.size(); i++) {
        std::string next_table = table_cardinalities[i].first;
        auto next_plan = generate_scan_plan(next_table, {});
        
        // 寻找与已连接表的连接条件
        std::vector<Condition> join_conditions;
        auto it = join_conds.begin();
        while (it != join_conds.end()) {
            if (!it->is_rhs_val) {
                bool left_in_joined = joined_tables.count(it->lhs_col.tab_name) > 0;
                bool right_in_joined = joined_tables.count(it->rhs_col.tab_name) > 0;
                bool left_is_next = it->lhs_col.tab_name == next_table;
                bool right_is_next = it->rhs_col.tab_name == next_table;
                
                if ((left_in_joined && right_is_next) || (right_in_joined && left_is_next)) {
                    join_conditions.push_back(*it);
                    it = join_conds.erase(it);
                } else {
                    ++it;
                }
            } else {
                ++it;
            }
        }
        
        result_plan = std::make_shared<JoinPlan>(T_NestLoop, result_plan, next_plan, join_conditions);
        joined_tables.insert(next_table);
    }
    
    return result_plan;
}

size_t QueryOptimizer::get_table_cardinality(const std::string& table_name) {
    // 直接使用表元数据中的精确记录数量
    TabMeta& tab = sm_manager_->db_.get_table(table_name);
    // 如果记录数量为0，返回1以避免连接顺序优化中的除零错误
    return std::max(tab.record_count, static_cast<size_t>(1));
}

size_t QueryOptimizer::estimate_join_cardinality(const std::string& left_table, const std::string& right_table) {
    size_t left_card = get_table_cardinality(left_table);
    size_t right_card = get_table_cardinality(right_table);
    
    // 简单的连接基数估算 - 假设连接选择性为 1/max(left, right)
    if (left_card == 0 || right_card == 0) {
        return 0;
    }
    
    return (left_card * right_card) / std::max(left_card, right_card);
}

std::shared_ptr<Plan> QueryOptimizer::apply_selection(std::shared_ptr<Plan> plan, const std::vector<Condition>& conds) {
    if (conds.empty()) {
        return plan;
    }
    
    return std::make_shared<FilterPlan>(plan, conds);
}

std::shared_ptr<Plan> QueryOptimizer::apply_projection(std::shared_ptr<Plan> plan, const std::vector<TabCol>& cols, bool is_select_star) {
    if (cols.empty()) {
        return plan;
    }
    
    return std::make_shared<ProjectionPlan>(T_Projection, plan, cols, is_select_star);
}

std::set<std::string> QueryOptimizer::get_plan_table_names(std::shared_ptr<Plan> plan) {
    std::set<std::string> table_names;
    
    if (!plan) {
        return table_names;
    }
    
    if (auto scan_plan = std::dynamic_pointer_cast<ScanPlan>(plan)) {
        table_names.insert(scan_plan->tab_name_);
    } else if (auto join_plan = std::dynamic_pointer_cast<JoinPlan>(plan)) {
        auto left_tables = get_plan_table_names(join_plan->left_);
        auto right_tables = get_plan_table_names(join_plan->right_);
        table_names.insert(left_tables.begin(), left_tables.end());
        table_names.insert(right_tables.begin(), right_tables.end());
    } else if (auto proj_plan = std::dynamic_pointer_cast<ProjectionPlan>(plan)) {
        return get_plan_table_names(proj_plan->subplan_);
    } else if (auto filter_plan = std::dynamic_pointer_cast<FilterPlan>(plan)) {
        return get_plan_table_names(filter_plan->subplan_);
    }
    
    return table_names;
}

bool QueryOptimizer::needs_projection(std::shared_ptr<Plan> plan, const std::vector<TabCol>& required_cols) {
    // 简单的启发式：如果是扫描节点且所需列不是全部列，则需要投影
    if (auto scan_plan = std::dynamic_pointer_cast<ScanPlan>(plan)) {
        // 检查是否需要所有列
        if (required_cols.empty()) {
            return false;
        }
        
        // 获取表的所有列
        try {
            TabMeta& tab = sm_manager_->db_.get_table(scan_plan->tab_name_);
            if (required_cols.size() < tab.cols.size()) {
                return true;  // 需要的列少于总列数，需要投影
            }
        } catch (...) {
            // 如果获取表信息失败，保守地假设需要投影
            return true;
        }
    }
    
    // 对于连接和其他复合操作，更保守的策略：
    // 只有当明确需要列过滤时才添加投影
    if (auto join_plan = std::dynamic_pointer_cast<JoinPlan>(plan)) {
        // 对于连接节点，通常不需要中间投影，让顶层统一处理
        return false;
    }
    
    // 对于其他类型的节点，只有在有明确列需求时才投影
    return !required_cols.empty();
}



std::shared_ptr<Plan> QueryOptimizer::pushdown_selection(std::shared_ptr<Plan> plan, std::vector<Condition>& conds) {
    if (!plan || conds.empty()) {
        return plan;
    }
    
    // 根据计划类型进行选择下推
    if (auto scan_plan = std::dynamic_pointer_cast<ScanPlan>(plan)) {
        // 扫描节点：检查是否有适用的条件，但不直接下推到扫描节点
        // 而是在扫描节点上方创建Filter节点
        std::vector<Condition> applicable_conds;
        auto it = conds.begin();
        while (it != conds.end()) {
            // 检查条件是否只涉及当前表
            if (it->lhs_col.tab_name == scan_plan->tab_name_ && it->is_rhs_val) {
                applicable_conds.push_back(*it);
                it = conds.erase(it);  // 从原条件列表中移除
            } else {
                ++it;
            }
        }
        
        // 如果有适用的条件，在扫描节点上方创建Filter节点
        if (!applicable_conds.empty()) {
            return std::make_shared<FilterPlan>(plan, applicable_conds);
        }
        
    } else if (auto join_plan = std::dynamic_pointer_cast<JoinPlan>(plan)) {
        // 连接节点：根据条件涉及的表来决定下推到哪个子树
        std::set<std::string> left_tables = get_plan_table_names(join_plan->left_);
        std::set<std::string> right_tables = get_plan_table_names(join_plan->right_);
        
        std::vector<Condition> left_conds;
        std::vector<Condition> right_conds;
        std::vector<Condition> remaining_conds;
        
        // 分析每个条件应该下推到哪个子树
        for (const auto& cond : conds) {
            if (cond.is_rhs_val) {
                // 单表条件：检查左侧列属于哪个子树
                if (left_tables.count(cond.lhs_col.tab_name) > 0) {
                    left_conds.push_back(cond);
                } else if (right_tables.count(cond.lhs_col.tab_name) > 0) {
                    right_conds.push_back(cond);
                } else {
                    remaining_conds.push_back(cond);
                }
            } else {
                // 双表条件：检查是否跨越左右子树
                bool left_has_lhs = left_tables.count(cond.lhs_col.tab_name) > 0;
                bool left_has_rhs = left_tables.count(cond.rhs_col.tab_name) > 0;
                bool right_has_lhs = right_tables.count(cond.lhs_col.tab_name) > 0;
                bool right_has_rhs = right_tables.count(cond.rhs_col.tab_name) > 0;
                
                if ((left_has_lhs && left_has_rhs) && !(right_has_lhs || right_has_rhs)) {
                    // 条件完全在左子树内
                    left_conds.push_back(cond);
                } else if ((right_has_lhs && right_has_rhs) && !(left_has_lhs || left_has_rhs)) {
                    // 条件完全在右子树内
                    right_conds.push_back(cond);
                } else {
                    // 条件跨越左右子树，无法下推
                    remaining_conds.push_back(cond);
                }
            }
        }
        
        // 递归下推到左右子树
        auto optimized_left = pushdown_selection(join_plan->left_, left_conds);
        auto optimized_right = pushdown_selection(join_plan->right_, right_conds);
        
        // 更新剩余条件
        conds = remaining_conds;
        
        // 创建新的连接计划
        auto new_join_plan = std::make_shared<JoinPlan>(join_plan->tag, optimized_left, optimized_right, join_plan->conds_);
        new_join_plan->type = join_plan->type;
        
        return new_join_plan;
        
    } else if (auto proj_plan = std::dynamic_pointer_cast<ProjectionPlan>(plan)) {
        // 投影节点：下推到子计划
        auto optimized_subplan = pushdown_selection(proj_plan->subplan_, conds);
        
        return std::make_shared<ProjectionPlan>(proj_plan->tag, optimized_subplan, proj_plan->sel_cols_);
    }
    
    // 对于无法下推的条件，在当前节点上方添加Filter
    if (!conds.empty()) {
        auto filter_plan = std::make_shared<FilterPlan>(plan, conds);
        conds.clear();  // 条件已应用，清空列表
        return filter_plan;
    }
    
    return plan;
}

std::shared_ptr<Plan> QueryOptimizer::pushdown_projection(std::shared_ptr<Plan> plan, std::vector<TabCol>& required_cols) {
    if (!plan) {
        return plan;
    }

    // 如果是SELECT *，不对子树做任何投影下推，直接返回原计划
    // 判断方法：required_cols等于所有表的所有列
    bool is_select_all = false;
    if (!required_cols.empty()) {
        size_t total_cols = 0;
        for (const auto& table_name : get_plan_table_names(plan)) {
            try {
                TabMeta& tab = sm_manager_->db_.get_table(table_name);
                total_cols += tab.cols.size();
            } catch (...) {}
        }
        if (required_cols.size() == total_cols) {
            is_select_all = true;
        }
    }
    if (is_select_all) {
        return plan;
    }

    // 根据计划类型进行投影下推
    if (auto scan_plan = std::dynamic_pointer_cast<ScanPlan>(plan)) {
        // 扫描节点：无需特殊处理，扫描会读取所有列
        // 实际的列选择会在上层的Projection节点中处理
        return plan;
    }

    else if (auto join_plan = std::dynamic_pointer_cast<JoinPlan>(plan)) {
        // 连接节点：分析哪些列是左右子树需要的
        std::vector<TabCol> left_required_cols;
        std::vector<TabCol> right_required_cols;

        // 获取左右子树涉及的表名
        std::set<std::string> left_tables = get_plan_table_names(join_plan->left_);
        std::set<std::string> right_tables = get_plan_table_names(join_plan->right_);

        // 分配所需列到左右子树
        for (const auto& col : required_cols) {
            if (left_tables.count(col.tab_name) > 0) {
                left_required_cols.push_back(col);
            }
            if (right_tables.count(col.tab_name) > 0) {
                right_required_cols.push_back(col);
            }
        }

        // 添加连接条件中涉及的列
        for (const auto& cond : join_plan->conds_) {
            // 左侧列
            TabCol left_col = cond.lhs_col;
            if (left_tables.count(left_col.tab_name) > 0) {
                if (std::find(left_required_cols.begin(), left_required_cols.end(), left_col) == left_required_cols.end()) {
                    left_required_cols.push_back(left_col);
                }
            }
            if (right_tables.count(left_col.tab_name) > 0) {
                if (std::find(right_required_cols.begin(), right_required_cols.end(), left_col) == right_required_cols.end()) {
                    right_required_cols.push_back(left_col);
                }
            }

            // 右侧列（如果不是值）
            if (!cond.is_rhs_val) {
                TabCol right_col = cond.rhs_col;
                if (left_tables.count(right_col.tab_name) > 0) {
                    if (std::find(left_required_cols.begin(), left_required_cols.end(), right_col) == left_required_cols.end()) {
                        left_required_cols.push_back(right_col);
                    }
                }
                if (right_tables.count(right_col.tab_name) > 0) {
                    if (std::find(right_required_cols.begin(), right_required_cols.end(), right_col) == right_required_cols.end()) {
                        right_required_cols.push_back(right_col);
                    }
                }
            }
        }

        // 递归下推到子树
        auto optimized_left = pushdown_projection(join_plan->left_, left_required_cols);
        auto optimized_right = pushdown_projection(join_plan->right_, right_required_cols);

        // 如果子树的所需列发生了变化，在子树上添加投影
        if (!left_required_cols.empty() && needs_projection(join_plan->left_, left_required_cols)) {
            optimized_left = std::make_shared<ProjectionPlan>(T_Projection, optimized_left, left_required_cols);
        }
        if (!right_required_cols.empty() && needs_projection(join_plan->right_, right_required_cols)) {
            optimized_right = std::make_shared<ProjectionPlan>(T_Projection, optimized_right, right_required_cols);
        }

        // 创建新的连接计划
        auto new_join_plan = std::make_shared<JoinPlan>(join_plan->tag, optimized_left, optimized_right, join_plan->conds_);
        new_join_plan->type = join_plan->type;

        return new_join_plan;

    } else if (auto proj_plan = std::dynamic_pointer_cast<ProjectionPlan>(plan)) {
        // 投影节点：合并投影需求
        std::vector<TabCol> merged_cols = required_cols;

        // 递归下推到子计划
        auto optimized_subplan = pushdown_projection(proj_plan->subplan_, merged_cols);

        return std::make_shared<ProjectionPlan>(proj_plan->tag, optimized_subplan, proj_plan->sel_cols_);

    } else if (auto filter_plan = std::dynamic_pointer_cast<FilterPlan>(plan)) {
        // 过滤节点：添加过滤条件中涉及的列
        std::vector<TabCol> extended_cols = required_cols;

        for (const auto& cond : filter_plan->conds_) {
            TabCol left_col = cond.lhs_col;
            if (std::find(extended_cols.begin(), extended_cols.end(), left_col) == extended_cols.end()) {
                extended_cols.push_back(left_col);
            }

            if (!cond.is_rhs_val) {
                TabCol right_col = cond.rhs_col;
                if (std::find(extended_cols.begin(), extended_cols.end(), right_col) == extended_cols.end()) {
                    extended_cols.push_back(right_col);
                }
            }
        }

        // 递归下推到子计划
        auto optimized_subplan = pushdown_projection(filter_plan->subplan_, extended_cols);

        return std::make_shared<FilterPlan>(optimized_subplan, filter_plan->conds_);
    }

    return plan;
}