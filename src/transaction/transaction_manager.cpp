/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#include "transaction_manager.h"
#include "record/rm_file_handle.h"
#include "system/sm_manager.h"

std::unordered_map<txn_id_t, Transaction *> TransactionManager::txn_map = {};

/**
 * @description: 事务的开始方法
 * @return {Transaction*} 开始事务的指针
 * @param {Transaction*} txn 事务指针，空指针代表需要创建新事务，否则开始已有事务
 * @param {LogManager*} log_manager 日志管理器指针
 */
Transaction * TransactionManager::begin(Transaction* txn, LogManager* log_manager) {
    if (txn == nullptr) {
        txn = new Transaction(next_txn_id_++);
    }
    txn_map[txn->get_transaction_id()] = txn;

    if (log_manager != nullptr) {
        BeginLogRecord beginLog(txn->get_transaction_id());
        log_manager->add_log_to_buffer(&beginLog);
    }
    return txn;
}

/**
 * @description: 事务的提交方法
 * @param {Transaction*} txn 需要提交的事务
 * @param {LogManager*} log_manager 日志管理器指针
 */
void TransactionManager::commit(Transaction* txn, LogManager* log_manager) {
}

/**
 * @description: 事务的终止（回滚）方法
 * @param {Transaction *} txn 需要回滚的事务
 * @param {LogManager} *log_manager 日志管理器指针
 */
void TransactionManager::abort(Transaction* txn, LogManager* log_manager) {
    // Todo:
    // 1. 回滚所有写操作
    // 2. 释放所有锁
    // 3. 清空事务相关资源，eg.锁集
    // 4. 把事务日志刷入磁盘中
    // 5. 更新事务状态
    if (txn == nullptr)
        return;
    auto writeSet = txn->get_write_set();
    while (!writeSet->empty()) {
        auto& item = writeSet->back();
        auto context = new Context(lock_manager_, log_manager, txn);
        switch (item->GetWriteType()) {
        case WType::INSERT_TUPLE:
            sm_manager_->rollback_insert(item->GetTableName(), item->GetRid(), context);
            break;
        case WType::UPDATE_TUPLE:
            sm_manager_->rollback_update(item->GetTableName(), item->GetRid(), item->GetRecord(), context);
            break;
        case WType::DELETE_TUPLE:
            sm_manager_->rollback_delete(item->GetTableName(), item->GetRecord(), context);
            break;
        default:
            break;
        }
        writeSet->pop_back();
    }
    writeSet->clear();

    auto lockSet = txn->get_lock_set();
    for(auto it : *lockSet){
        lock_manager_->unlock(txn, it);
    }
    lockSet->clear();
    txn->set_state(TransactionState::ABORTED);
}